<script lang="ts" name="createRefundApply" setup>
import { h, ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRefundApplyApi } from '/@/api/outpatientDoctor/refundApply';
import { auth } from '/@/utils/authFunction';
import { formatNumber } from '/@/utils/formatObject';
import { ElDivider } from 'element-plus'
import { formatOnlyDate } from '/@/utils/formatTime';
import { useRegisterApi } from '/@/api/outpatientDoctor/register';
import { useVisitApi } from '/@/api/outpatientDoctor/visit';
import { useOutpatientChargeApi } from '../../../api/outpatientDoctor/charge';
import ElAntList from '/@/components/list/elAntList.vue';
import { SysUser<PERSON><PERSON>, SysOrg<PERSON>pi } from '/@/api-services/api';
import { getAPI } from '/@/utils/axios-utils';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';

const outpatientChargeApi = useOutpatientChargeApi();
const registerApi = useRegisterApi();
const visitApi = useVisitApi();
const basicInfoApi = useBasicInfoApi();
const spacer = h(ElDivider, { direction: 'vertical' })
const refundApplyApi = useRefundApplyApi();
const ruleFormRef = ref();

const state = reactive({
    visitQueryParams: {
        visitTimeRange: [] as any[],
        cardNo: '',
    } as any,
    visitInfo: {} as any,
    title: '',
    loading: false,
    tableLoading: false,
    selectData: [] as any[],
    tableData: [] as any[],
    visitList: [] as any[],
    showDialog: false,
    ruleForm: {} as any,

    stores: {},
    dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
});

// 页面加载时
onMounted(async () => {

    // 设置当天日期作为默认就诊日期范围
    const today = new Date();
    // 开始时间往前推一天
    const startOfDay = new Date(today);
    startOfDay.setDate(today.getDate() - 1); // 往前推一天
    startOfDay.setHours(0, 0, 0, 0);

    // 结束时间往后推一天
    const endOfDay = new Date(today);
    endOfDay.setDate(today.getDate() + 1); // 往后推一天
    endOfDay.setHours(23, 59, 59, 999);

    // 格式化日期
    const startTimeStr = formatDate(startOfDay, 'YYYY-mm-dd');
    const endTimeStr = formatDate(endOfDay, 'YYYY-mm-dd');

    // 设置日期范围
    state.visitQueryParams.visitTimeRange = [startTimeStr, endTimeStr];
    state.visitInfo = null;
    basicInfoApi.getUsers({}).then((res: any) => {
        state.dropdownData.users = res.data.result;
    });
    basicInfoApi.getDepartments({}).then((res: any) => {
        state.dropdownData.depts = res.data.result;
    });

});
const getbillingTypeName = (row: any) => {
    switch (row.billingType) {
        case "Prescription":
            return '处方'
        case "prescription":
            return '处方'
        case "Dispose":
            return '处置'
        case "Examination":
            return '检查'
        case "LabTest":
            return '检验'
        default:
            return ''
    }

}

const getCurrentStatus = (row: any) => {
    if (row.refundApplyId == null || row.refundApplyId == 0) {
        return '未申请';
    } else {
        if (row.auditStatus == 0) {
            return '未审核';
        } else if (row.auditStatus == 1) {
            return '审核中';
        } else if (row.auditStatus == 2) {
            return '已驳回';
        } //3  审核完成  4  退费完成
        else if (row.auditStatus == 3) {
            return '审核完成';
        } else if (row.auditStatus == 4) {
            return '退费完成';
        } else {
            return '未知状态';
        }

    }
}
const getUserName = (userId: any) => {

    if (!userId || !state.dropdownData.users) return '';
    const user = state.dropdownData.users.find((item: any) => item.id == userId);
    return user ? user.realName : userId;
}
const getDeptName = (deptId: any) => {
    if (!deptId || !state.dropdownData.depts) return deptId;
    const dept = state.dropdownData.depts.find((item: any) => item.id == deptId);
    return dept ? dept.name : deptId;
}
// 查询操作
const handleQuery = async (params: any = {}) => {
    state.visitInfo = null;
    state.tableData = []
    state.visitList = [];
    queryTreatInfo();




};
const queryTreatInfo = async () => {
    console.log('查询患者就诊信息', state.visitQueryParams);
    if (state.visitQueryParams.cardNo) {

        visitApi.getList(state.visitQueryParams).then(res => {
            // 添加到患者列表  state.patientList
            // state.visitList = res.data.result?.items ?? [];
            state.visitList = res.data.result ?? []
            console.log('患者列表', state.visitList);

        }).catch(err => {
            // console.error('Failed to fetch patient info:', err);
        })
    }
};
const onSelectPrescription = (row: any) => {

    state.visitInfo = row.item;
    state.tableData = [];
    console.log('就诊信息', state.visitInfo);

    getListOfRefund();

};

const getListOfRefund = async () => {
    state.tableLoading = true;
    outpatientChargeApi.ListOfRefund({
        visitNo: state.visitInfo.visitNo

    }).then(res => {
        // 添加到患者列表  state.patientList
        console.log('ListOfRefund', res.data.result);
        state.tableData = res.data.result;
        state.tableLoading = false;
    }).catch(err => {
        state.tableLoading = false;
        // console.error('Failed to fetch patient info:', err);a
    })
}


const apply = async (row: any) => {

    if (row.applyReason == '' || row.applyReason == null) {
        ElMessage({
            message: '退费原因不能为空',
            type: "error",
        })
    }

    const values = {
        chargeId: row.chargeId,
        patientName: state.visitInfo.patientName,
        applyReason: row.applyReason

    };
    state.tableLoading = true;
    await refundApplyApi.add(values)
        .then(res => {
            getListOfRefund();
        }).catch(err => {
            state.tableLoading = false;
        });

};
const getAgeUnit = ((ageUnit: any) => {
    if (ageUnit === "0")
        return '岁'
    else if (ageUnit === "1")
        return '月'
    else if (ageUnit === "2")
        return '天'
    else return ''

});

const getColumnLabel = (itemType: string) => {
    switch (itemType) {
        case '1': // 假设1代表药品
            return '药品名称';
        case '2': // 假设2代表项目
            return '项目名称';
        default:
            return '名称';
    }
};
// 添加ref用于获取表格实例
const tableRef = ref();

// 添加处理行点击的方法
const handleRowClick = (row: any) => {
    // 获取当前展开行的状态
    const expanded = tableRef.value.store.states.expandRows.value.includes(row);

    // 切换展开状态
    if (expanded) {
        tableRef.value.store.states.expandRows.value =
            tableRef.value.store.states.expandRows.value.filter((r: any) => r !== row);
    } else {
        tableRef.value.store.states.expandRows.value.push(row);
    }
};
</script>
<template>
    <div class="refundApply-container">
        <!-- <el-card shadow="hover" :body-style="{ padding: '15px' }">

            <el-row :gutter="10">
                <el-col :span="18">
                    <el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
                        <el-descriptions :column="2" border>
                            <el-descriptions-item label="就诊时间" label-align="right">
                                <el-form-item prop="outpatientNo">
                                    <el-date-picker v-model="state.ruleForm.treatTime" type="datetimerange"
                                        format="YYYY-MM-DD HH:mm:ss" date-format="YYYY/MM/DD ddd"
                                        time-format="A hh:mm:ss" />
                                </el-form-item>
                            </el-descriptions-item>
                            <el-descriptions-item label="门诊号" label-align="right">
                                <el-form-item prop="outpatientNo">
                                    <el-input v-model="state.ruleForm.outpatientNo" placeholder="" maxlength="100"
                                        show-word-limit />
                                </el-form-item>

                            </el-descriptions-item>
                            <el-descriptions-item label="退费原因" label-align="right">
                                <el-input v-model="state.ruleForm.applyReason" placeholder="请输入退费原因" maxlength="100"
                                    show-word-limit clearable />
                            </el-descriptions-item>
                        </el-descriptions>
                    </el-form>
                </el-col>
                <el-col :span="6">
                    <el-button-group style="position: absolute;   right: 0;">

                        <el-button type="info" icon="ele-Search" @click="handleQuery" v-reclick="2000">查询</el-button>
                        <el-button type="success" icon="ele-Reading" v-reclick="2000">读卡</el-button>
                        <el-button type="success" icon="ele-Reading" v-reclick="2000">读电子健康卡</el-button>

                    </el-button-group>
                    <el-button style="position: absolute; bottom: 0; right: 0;" type="primary" icon="ele-Finished"
                        v-reclick="5000">保存</el-button>
                </el-col>
            </el-row>

        </el-card> -->
        <el-container>
            <el-aside width="340px" v-loading="state.loading">
                <el-card shadow="hover" class="full-table">
                    <el-form :model="state.visitQueryParams" ref="queryForm">
                        <el-form-item>
                            <el-button-group>

                                <el-button type="success" icon="ele-Reading" v-reclick="2000">读卡</el-button>
                                <el-button type="success" icon="ele-Reading" v-reclick="2000">读电子健康卡</el-button>
                                <el-button type="info" icon="ele-Search" @click="handleQuery"
                                    v-reclick="2000">查询</el-button>
                            </el-button-group>

                        </el-form-item>
                        <el-form-item prop="visitTimeRange">
                            <el-date-picker v-model="state.visitQueryParams.visitTimeRange" type="datetimerange"
                                format="YYYY-MM-DD HH:mm:ss" start-placeholder="就诊时间开始" end-placeholder="就诊时间结束" />
                        </el-form-item>
                        <!-- <el-button type="info" plain icon="ele-Search" style="float: right; margin-left: 2px;"> 查询
                        </el-button> -->
                        <el-form-item style="margin-bottom: 8px">
                            <el-input v-model="state.visitQueryParams.cardNo" placeholder="输入卡号">
                            </el-input>
                        </el-form-item>
                    </el-form>

                    <div class="list-wrapper">
                        <!-- v-model:selectedKey="state.currentPrescription.id" -->
                        <el-ant-list :data="state.visitList" :showAvatar="false" @select="onSelectPrescription">
                            <template #title="{ item }">
                                {{ item.patientName }}
                                <!-- <span style="margin-left: 15px; font-size: 12px;"> {{ item.age
									}}{{ getAgeUnit(item.ageUnit) }}</span> -->
                            </template>
                            <template #description="{ item }">
                                <span style="font-size: 12px"> {{ item.age }}{{ getAgeUnit(item.ageUnit) }}</span>

                                <g-sys-dict style="margin-left: 15px" v-model="item.sex" code="GenderEnum" />

                                <!-- <el-tag type="warning">  

								门诊号： {{ item.outpatientNo }}
							  </el-tag> -->
                            </template>

                            <template #extra="{ item }">
                                <el-tag type="warning"> {{ item.regCategory }} </el-tag>
                            </template>

                            <template #status="{ item }">
                                <!-- 就诊状态-->
                                <g-sys-dict v-model="item.visitType" code="VisitType" />
                            </template>

                            <template #footer="{ item }">
                                <span> {{ item.createTime }}</span>
                            </template>
                        </el-ant-list>
                    </div>
                </el-card>
            </el-aside>
            <el-main style="padding: 0px 0px 0px 10px;">

                <el-card class="full-table" shadow="hover" :body-style="{ padding: '10px' }">

                    <el-space :size="5" :spacer="spacer" v-if="state.visitInfo != null">

                        <el-text class="mx-1" size="large">{{ state.visitInfo.patientName ?? "" }}</el-text>

                        <el-text class="mx-1" size="small"><g-sys-dict v-model="state.visitInfo.sex"
                                code="GenderEnum" /></el-text>

                        <el-text><span style="margin-right: 5px;">{{ state.visitInfo.age
                                }}{{ getAgeUnit(state.visitInfo.ageUnit) ?? '' }}</span> {{
                                    formatOnlyDate(state.visitInfo.birthday
                                        ?? '') }} </el-text>
                        <el-text> {{ state.visitInfo.deptName }}[{{ state.visitInfo.doctorName }} ]</el-text>
                        <el-text>门诊号：{{ state.visitInfo.outpatientNo }} </el-text>
                        <el-text>就诊号：{{ state.visitInfo.visitNo }} </el-text>
                        <el-text>就诊时间：{{ state.visitInfo.createTime }} </el-text>
                        <el-text>卡号：{{ state.visitInfo.cardNo }} </el-text>
                        <el-text> {{ state.visitInfo.feeName }} </el-text>

                        <el-text> <el-tag type="danger">余额：{{ state.visitInfo.balance }}</el-tag> </el-text>
                    </el-space>
                    <!-- <el-form :model="state.visitQueryParams" ref="queryForm">

                        <el-form-item label="退费原因" style="margin-top: 20px">
                            <el-input placeholder="退费原因">
                            </el-input>
                        </el-form-item>
                    </el-form> -->
                    <el-table stripe :data="state.tableData" style="margin-top: 20px;" tooltip-effect="light"
                        v-loading="state.tableLoading" highlight-current-row row-key="id" border ref="tableRef">
                        <!-- @row-click="handleRowClick"      default-expand-all 
                        > -->

                        <!-- 
                        <el-table-column fixed="right" label="退费原因" width="500px">
                            <template #default="scope">
                                <div @click.stop class="refund-container">
                                    <el-input v-model="scope.row.refundReason" placeholder="退费原因">
                                    </el-input>
                                    <el-button v-if="scope.row.refundApplyId == null || scope.row.refundApplyId == 0"
                                        icon="ele-Finished" size="small" type="warning" v-reclick="5000"
                                        @click.stop="apply(scope.row)" v-auth="'storageRefundRecord:update'">
                                        申请退费
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column> -->

                        <el-table-column type="expand" label="展开" width="55" align="center" fixed="left">
                            <template #default="props">

                                <!-- <div @click.stop class="refund-container">
                                    <el-input v-model="props.row.refundReason" placeholder="退费原因">
                                    </el-input>
                                    <el-button v-if="props.row.refundApplyId == null || props.row.refundApplyId == 0"
                                        icon="ele-Finished" size="small" type="warning" v-reclick="5000"
                                        @click.stop="apply(props.row)" v-auth="'storageRefundRecord:update'">
                                        申请退费
                                    </el-button>
                                </div> -->





                                <div style="margin-left: 150px;margin-bottom: 20px;">
                                    <el-row :gutter="10">


                                        <el-col :span="8">

                                            <el-form-item label="退费原因" class="custom-form-item">
                                                <el-input v-model="props.row.applyReason" placeholder="退费原因">
                                                </el-input>
                                            </el-form-item>


                                        </el-col>
                                        <el-col :span="4">
                                            <el-form-item>
                                                <el-button
                                                    v-if="props.row.refundApplyId == null || props.row.refundApplyId == 0"
                                                    icon="ele-Finished" size="small" type="warning" v-reclick="5000"
                                                    @click.stop="apply(props.row)" v-auth="'refundApply:add'">
                                                    申请退费
                                                </el-button>
                                            </el-form-item>
                                        </el-col>

                                    </el-row>
                                    <el-table id="prescriptionTable" :data="props.row.details" border
                                        class="success-row" :class="{ 'success-row': true }"
                                        header-cell-class-name="sub-table-header" cell-class-name="sub-table-cell">
                                        <el-table-column prop='itemName' label='药品名称' show-overflow-tooltip
                                            width="140" />

                                        <el-table-column prop='spec' label='规格' show-overflow-tooltip />
                                        <el-table-column prop='number' label='数量' show-overflow-tooltip>
                                            <template #default="scope">
                                                <span>{{ scope.row.quantity }} {{ scope.row.unit }}</span>
                                            </template>
                                        </el-table-column>

                                        <el-table-column prop='price' label='价格' show-overflow-tooltip align="right">
                                            <template #default="scope">
                                                <span>{{ formatNumber(scope.row.price) }}</span>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop='amount' label='金额' show-overflow-tooltip align="right">
                                            <template #default="scope">
                                                <span>{{ formatNumber(scope.row.amount) }}</span>
                                            </template>
                                        </el-table-column>

                                        <el-table-column prop='manufacturer' label='生产厂家' show-overflow-tooltip
                                            width="140" />
                                        <el-table-column prop='medicineCode' label='国家医保编码' show-overflow-tooltip
                                            width="140" />
                                        <el-table-column prop='itemCode' label='药品编码' show-overflow-tooltip />

                                        <el-table-column prop='chargeCategoryCode' label='收费类别' show-overflow-tooltip
                                            width="140" />
                                    </el-table>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop='billingNo' label='申请单号/处方号' show-overflow-tooltip width="140" />
                        <el-table-column prop='billingType' label='类别' show-overflow-tooltip width="60">

                            <template #default="scope">

                                {{ getbillingTypeName(scope.row) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop='createTime' label='收费时间' show-overflow-tooltip width="140" />


                        <el-table-column prop='billingTime' label='开单时间' show-overflow-tooltip width="140" />
                        <el-table-column prop='billingDeptId' label='开单科室' show-overflow-tooltip>
                            <template #default="scope">
                                <span>{{ getDeptName(scope.row.billingDeptId) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop='billingDoctorId' label='开单医生' show-overflow-tooltip>
                            <template #default="scope">
                                <span>{{ getUserName(scope.row.billingDoctorId) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop='executeDeptId' label='执行科室' show-overflow-tooltip>
                            <template #default="scope">
                                <span>{{ getDeptName(scope.row.executeDeptId) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop='executeDoctorId' label='执行医生' show-overflow-tooltip>
                            <template #default="scope">
                                <span>{{ getUserName(scope.row.executeDoctorId) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop='applyStatus' label='状态' show-overflow-tooltip width="140">
                            <template #default="scope">
                                <span>{{ getCurrentStatus(scope.row) }}</span>

                            </template>
                        </el-table-column>

                        <el-table-column prop='applyTime' label='操作时间' show-overflow-tooltip />
                        <el-table-column prop='applyUserName' label='操作人' show-overflow-tooltip />

                    </el-table>

                </el-card>

            </el-main>
        </el-container>
    </div>
</template>
<style lang="scss" scoped>
.el-card.full-table {
    height: calc(100vh - 100px);
}

.list-wrapper {
    height: calc(100vh - 250px);
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    overflow: hidden;

}

.list-wrapper:hover {
    overflow-y: scroll;
    /* 鼠标悬停时显示滚动条 */
}


.refund-container {
    display: flex;
    gap: 10px;
    align-items: center;

    .el-input {
        flex: 1;
    }
}

:deep(.sub-table-header) {
    background: #e9ebec !important;

}

:deep(.sub-table-cell) {
    background: #d8dadd !important;
}

:deep(.el-table) {


    // 修改选中行的背景颜色
    .current-row td {
        background-color: #86b9f7 !important;
    }

    // 鼠标悬停时的背景颜色
    .el-table__row:hover td {
        background-color: #d0e0f3 !important;
    }

    // 确保选中行的背景色优先级更高
    .current-row:hover td {
        background-color: #86b9f7 !important;
    }
}
</style>
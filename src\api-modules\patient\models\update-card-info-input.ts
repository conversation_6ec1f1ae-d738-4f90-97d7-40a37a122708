/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { BusinessTypeEnum } from './business-type-enum';
import { CardStatusEnum } from './card-status-enum';
import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 就诊卡管理更新输入参数
 *
 * @export
 * @interface UpdateCardInfoInput
 */
export interface UpdateCardInfoInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateCardInfoInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateCardInfoInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateCardInfoInput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    tenantId?: number | null;

    /**
     * 患者唯一号
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    patientNo?: string | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    name?: string | null;

    /**
     * 英文姓名
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    englishName?: string | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    wubiCode?: string | null;

    /**
     * @type {GenderEnum}
     * @memberof UpdateCardInfoInput
     */
    sex?: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    age?: number;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    ageUnit?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof UpdateCardInfoInput
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof UpdateCardInfoInput
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    idCardNum?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    nation?: string | null;

    /**
     * 电话号码
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    phone?: string | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    contactName?: string | null;

    /**
     * 联系人关系
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    contactRelationship?: string | null;

    /**
     * 联系人地址
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    contactAddress?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    contactPhone?: string | null;

    /**
     * 国籍
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    nationality?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    occupation?: string | null;

    /**
     * 婚姻
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    marriage?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    nativePlaceCounty?: number | null;

    /**
     * 出生地省
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    birthplaceProvince?: number | null;

    /**
     * 出生地市
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    birthplaceCity?: number | null;

    /**
     * 出生地县
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    birthplaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    residenceProvince?: number;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    residenceCity?: number;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    residenceCounty?: number;

    /**
     * 详细现居住地
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    residenceAddress?: string | null;

    /**
     * 工作地址省
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    workProvince?: number | null;

    /**
     * 工作地址市
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    workCity?: number | null;

    /**
     * 工作地址县
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    workCounty?: number | null;

    /**
     * 详细工作地址
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    workAddress?: string | null;

    /**
     * 工作单位
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    workPlace?: string | null;

    /**
     * 单位电话
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    workPlacePhone?: string | null;

    /**
     * 医保类别
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    medInsCategory?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof UpdateCardInfoInput
     */
    medInsType?: MedInsTypeEnum;

    /**
     * 医疗类别（费别）
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    medCategory?: number | null;

    /**
     * 险种类型
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    insuranceType?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateCardInfoInput
     */
    isNoCard?: YesNoEnum;

    /**
     * 医保卡信息
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    medInsCardInfo?: string | null;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    cardNo?: string | null;

    /**
     * 患者ID
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    patientId?: number;

    /**
     * @type {BusinessTypeEnum}
     * @memberof UpdateCardInfoInput
     */
    businessType?: BusinessTypeEnum;

    /**
     * 使用科室
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    useDepts?: string | null;

    /**
     * 充值方式
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    chargeModes?: string | null;

    /**
     * 余额
     *
     * @type {number}
     * @memberof UpdateCardInfoInput
     */
    balance?: number | null;

    /**
     * @type {CardStatusEnum}
     * @memberof UpdateCardInfoInput
     */
    status?: CardStatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateCardInfoInput
     */
    remark?: string | null;
}

import { ref, reactive, onMounted, watch } from 'vue';
import { Frequency } from '/@/api-modules/shared/models';
// 中医诊断，证型
import { useTcmDiagnosisApi } from '/@/api/shared/tcmDiagnosis';
import { useTcmSyndromeApi } from '/@/api/shared/tcmSyndrome';
import { useIcd10Api } from '/@/api/shared/icd10';
import { ElMessageBox, ElMessage, ElTable } from 'element-plus';

import { getAPI } from '/@/utils/axios-utils';
import { FrequencyApi } from '/@/api-modules/shared/api';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { useDrugStorageApi } from '/@/api/pharmacy/drugStorage';

import { usePrescriptionApi } from '/@/api/outpatientDoctor/prescription';
import { useUserInfo } from '/@/stores/userInfo';

import { usePrescriptionDetailApi } from '/@/api/outpatientDoctor/prescriptionDetail';

export function usePrescriptionApplication() {
	const prescriptionDetailApi = usePrescriptionDetailApi();
	const userInfo = useUserInfo().$state.userInfos;
	const dictList = useUserInfo().dictList;
	// 诊断，中医诊断，证型
	const icd10Api = useIcd10Api();
	const tcmDiagnosisApi = useTcmDiagnosisApi();
	const tcmSyndromeApi = useTcmSyndromeApi();
	//处方api
	const prescriptionApi = usePrescriptionApi();
	// 途径频次

	const basicInfoApi = useBasicInfoApi();
	const drugStorageApi = useDrugStorageApi();
	const state = reactive({
		currentPatientInfo: {} as any, //当前病人信息
		loading: {
			detailLoading: false,
			mainLoading: false,
			icd10Loading: false,
		} as any,
		icd10Data: [] as any[], //icd10数据  ListLOfPatient

		stores: {},
		dropdownData: {} as any,
		tableParams: {
			page: 1,
			pageSize: 20,
			total: 0,
			field: 'createTime', // 默认的排序字段
			order: 'descending', // 排序方向
			descStr: 'descending', // 降序排序的关键字符
		},
		mainTableData: [] as any[], // 处方主表数据
		detailTableData: [] as any[], // 处方明细表
		detailSelectData: [] as any[],
		frequencyList: [] as Frequency[], // 频次列表
		routeList: [] as any[], // 途径列表
		drugSearchParam: {
			drugType: '1', // 药品查询类型
			drugTypeInclude: true, // 是否包含该分类
			storageId: [],
			zeroQuantity: 0,
			keyword: '', // 药品查询关键字
		},
		currentPrescriptionStatus: 'view' as string, // 当前处方状态 edit or view
		currentPrescription: {
			outpatientPrescriptionType: '1', // 处方类型

			prescriptionTime: new Date(), // 处方时间
			id: 0, // 处方id
		} as any, // 当前处方
	 
		currentDrug:{} as any,
		isHerbPrescription: false, // 是否中药处方
	});

	// 中医诊断
	const tcmDiagnosticRemoteMethod = async (query: string) => {
		const res = await basicInfoApi.GetTcmDiagnosis({ keyword: query });
		state.icd10Data = (res.data.result ?? []).map((item: any) => ({ code: item.tcmDiagnosisCode, name: item.tcmDiagnosisName }));
	};

	// 证型
	const tcmSyndromeCodeRemoteMethod = async (query: string) => {
		const res = await basicInfoApi.GetTcmSyndrome({ keyword: query });
		state.icd10Data = (res.data.result ?? []).map((item: any) => ({ code: item.tcmSyndromeCode, name: item.tcmSyndromeName }));
	};

	// icd10
	const icd10RemoteMethod = async (query: string) => {
		const res = await basicInfoApi.getIcd10s({ keyword: query });
		state.icd10Data = (res.data.result ?? []).map((item: any) => ({ code: item.code, name: item.name }));
	};

	//药品处方权限验证
	const checkDrugPermission = (_selectedDrug: any) => {
		//1 处方权限
		console.log('userInfo', userInfo);
		if (userInfo.prescriptionPermission != 1) {
			ElMessage({
				message: `您没有处方权无法开立处方！`,
				type: 'error',
			});
			return false;
		}

		//2 抗生素药品类型
		if (Number(_selectedDrug.antibacterialLevel) > 0) {
			if (userInfo.antibacterialPermission) {
				if (Number(userInfo.antibacterialPermission) < Number(_selectedDrug.antibacterialLevel)) {
					ElMessage({
						message: `暂无药品  ${_selectedDrug.drugName} 权限！`,
						type: 'error',
					});
					return;
				}
			}
			//选择预防用药还是治疗用药
		}
		//3 毒麻 权限

		// 0003
		// 毒品 麻醉、精神一类药品
		if (_selectedDrug.DrugCategory == '0003') {
			if (userInfo.narcoticPermission != 1) {
				ElMessage({
					message: `暂无药品  ${_selectedDrug.drugName} 权限！`,
					type: 'error',
				});
				return false;
			}
		}
		// 4  精神二类药品
		if (_selectedDrug.DrugCategory == '0002') {
			if (userInfo.psychotropicPermission != 1) {
				ElMessage({
					message: `暂无药品  ${_selectedDrug.drugName} 权限！`,
					type: 'error',
				});
				return false;
			}
		}
		// 5 中药处方权

		if (state.isHerbPrescription || _selectedDrug.drugType == '3') {
			if (userInfo.herbsPermission != 1) {
				ElMessage({
					message: `您没有中药处方权无法开立中医处方！`,
					type: 'error',
				});
				return false;
			}
		}

		return true;
	};

	//验证药品信息
	const validateDrug = (saveData: any) => {
		if (saveData.details.length === 0) {
			ElMessage.error('请添加药品！');
			return false;
		}

		if (!state.isHerbPrescription && saveData.details.length > 5) {
			ElMessage.error('药品不能超过5种！');
			return false;
		}
		// 中医处方
		if (state.isHerbPrescription) {
			if (saveData.main.tcmDiagnosticCode == null || saveData.main.tcmDiagnosticCode == '') {
				ElMessage.error('中医诊断不能为空！');
				return false;
			}
			if (saveData.main.tcmSyndromeCode == null || saveData.main.tcmSyndromeCode == '') {
				ElMessage.error('中医证型不能为空！');
				return false;
			}
			if (saveData.main.herbsQuantity == null || saveData.main.herbsQuantity == 0) {
				ElMessage.error('请输入付数！');
				return false;
			}
			if (state.currentPrescription.isDecoction == 1 && (saveData.main.herbsDecoction == null || saveData.main.herbsDecoction == '')) {
				ElMessage.error('请选择煎法！');
				return false;
			}
		}

		if (saveData.main.outpatientPrescriptionType == '1' && (saveData.main.diagnosticCode == null || saveData.main.diagnosticCode == '')) {
			ElMessage.error('主诊断不能为空！');
			return false;
		}
		if (saveData.main.outpatientPrescriptionType == '2' && (saveData.main.tcmDiagnosticCode == null || saveData.main.tcmDiagnosticCode == '')) {
			ElMessage.error('中医诊断不能为空！');
			return false;
		}
		debugger;
		saveData.main.patientId =state.currentPatientInfo.patientId;
		if (saveData.main.patientId == null || saveData.main.patientId == 0) {
			ElMessage.error('请选择就诊患者！');
			return false;
		}
		for (let i = 0; i < saveData.details.length; i++) {
			const drug = state.detailTableData[i];
			if (Number(drug.antibacterialLevel) > 0) {
				if (drug.medicationMethod == null || drug.medicationMethod == 0) {
					ElMessage.error(`药品${drug.drugName}请选择预防用药还是治疗用药！`);
					return false;
				}
			}
			if (state.isHerbPrescription) {
				// 验证用法
				if (drug.usageName == null || drug.usageName == '') {
					ElMessage.error(`药品${drug.drugName}煎服方法不能为空！`);
					return false;
				}
			}
			// 验证药品
			// 验证药品类型
			if (state.isHerbPrescription && drug.drugType != '3') {
				ElMessage.error(`药品${drug.drugName}不属于中药饮片`);
				return false;
			}
			if (!state.isHerbPrescription && drug.drugType == '3') {
				ElMessage.error(`药品${drug.drugName}属于中药饮片`);
				return false;
			}
			if (drug.singleDose == null || drug.singleDose == 0) {
				ElMessage.error('单次剂量不能为空！');
				return false;
			}
			if (drug.medicationDays == null || drug.medicationDays == 0) {
				ElMessage.error('天数不能为空！');
				return false;
			}
			if (drug.frequencyId == null || drug.frequencyId == 0) {
				ElMessage.error('频次不能为空！');
				return false;
			}
			if (drug.medicationRoutesId == null || drug.medicationRoutesId == 0) {
				ElMessage.error('途径不能为空！');
				return false;
			}
			if (!validateDrugDays(drug))
				// 用药天数验证
				return false;
		}

		return true;
	};
	// 用药天数验证
	const validateDrugDays = (drug: any) => {
		/*
  门诊处方用药量通常 不超过7天，慢性病或特殊情况可适当延长，但需医师明确注明理由。

2. 慢性病或特殊疾病
对于某些需长期服药的慢性病（如高血压、糖尿病等），处方用药量可延长至 不超过1个月，但需医师根据患者病情评估后决定，并在处方上注明。

3. 麻醉药品和精神药品
第一类精神药品：注射剂不超过3天，其他剂型不超过7天。

第二类精神药品：一般不超过7天，慢性病或特殊情况下可延长至不超过1个月。
  */
		const maxDays = 7; // 最大用药天数
		if (drug.medicationDays > maxDays) {
			ElMessage.error(`药品${drug.drugName}的用药天数超过${maxDays}天，请重新选择或调整用药天数。`);
			return false;
		} else {
			return true;
		}
	};

	// 查询操作
	const queryPrescriptionList = async () => {
		addNew();
		// 查询处方
		state.loading.mainLoading = true;
		await prescriptionApi
			.ListOfPatient({ visitNo: state.currentPatientInfo.visitNo })
			.then((res) => {
				state.mainTableData = res.data.result ?? []; // 处方主表数据

				for (let i = 0; i < state.mainTableData.length; i++) {
					// 门诊 处方类型
					var dictData = dictList['OutpatientPrescriptionType']?.find((x: any) => x.value == state.mainTableData[i].outpatientPrescriptionType);
					state.mainTableData[i].outpatientPrescriptionTagType = dictData?.tagType ?? 'primary';

					state.mainTableData[i].outpatientPrescriptionTypeName = dictData?.label ?? '普通处方';
				}

				state.loading.mainLoading = false;
			})
			.catch((err) => {
				state.loading.mainLoading = false;
			});
	};

	const queryPrescriptionDetailList = async (params: any = {}) => {
		// 查询明细
		if (state.currentPrescription.id == null || state.currentPrescription.id == 0) {
			return;
		}

		state.loading.detailLoading = true;

		await prescriptionApi
			.get(state.currentPrescription.id)
			.then((res) => {
				state.loading.detailLoading = false;
				if (res.data.code == 200) {
					state.currentPrescriptionStatus = 'view';
					state.detailTableData = res.data.result.details ?? []; // 处方明细数据
					state.currentPrescription = res.data.result.main; // 处方数据
				} else {
					ElMessage.error(res.data.msg);
				}
			})
			.catch((err) => {
				state.loading.detailLoading = false;
			});
	};
	const initData = async () => {
		 await basicInfoApi.getFrequencies({})
		.then((res) => { 
			state.frequencyList = res.data.result ?? [];
			 
		});
	
		basicInfoApi.getMedicationRoutes({}).then((res) => {
			state.routeList = res.data.result ?? [];
		});
 
	};
	const savePrescription = async () => {
		// 验证通过，提交表单
		const saveData = {
			main: state.currentPrescription,
			details: state.detailTableData,
		};

		saveData.main.cardNo = state.currentPatientInfo.cardNo;
		saveData.main.outpatientNo = state.currentPatientInfo.outpatientNo;
		saveData.main.visitNo = state.currentPatientInfo.visitNo;
		console.log('saveData', saveData);

		if (!validateDrug(saveData)) {
			return;
		}

		state.loading.detailLoading = true;
		await prescriptionApi[saveData.main.id ? 'update' : 'add'](saveData)
			.then((res) => {
				state.loading.detailLoading = false;
				if (res.data.code == 200) {
					ElMessage.success('保存成功');
					state.currentPrescriptionStatus = 'view';
					state.detailTableData = []; // 清空明细数据
					state.currentPrescription = {}; // 清空处方数据

					queryPrescriptionList();
					state.currentPrescription.id = res.data.result; // 处方id
					queryPrescriptionDetailList();
				} else {
					ElMessage.error(res.data.msg);
				}
			})
			.catch((err) => {
				state.loading.detailLoading = false;
			});
	};
	const addNew = async () => {
		const lastType = state.currentPrescription.outpatientPrescriptionType;
		const patientInfo = state.currentPatientInfo;
		//初始化当前处方
		state.currentPrescription = {
			id: 0,
			outpatientPrescriptionType: lastType, // 处方类型
			prescriptionTime: new Date(), // 处方时间
			patientId: patientInfo.patientId, // 患者id
			patientName: patientInfo.patientName, // 患者姓名
			registerId: patientInfo.id, // 挂号id
			visitNo: patientInfo.visitNo, // 就诊号
			cardNo: patientInfo.cardNo, // 卡号
			outpatientNo: patientInfo.outpatientNo, // 门诊号
			diagnosticCode: patientInfo.diagnosticCode, // 诊断编码
			diagnosticName: patientInfo.diagnosticName, // 诊断名称
		};
		state.currentPrescriptionStatus = 'edit';
		state.currentPrescription.id = 0; // 处方id
		state.detailTableData = []; // 清空明细数据
		state.detailSelectData = []; // 清空选中数据
	};

	const setMedicationRoutes = (row: any) => {
		// 更新frequencyName
		row.frequencyName = state.frequencyList.find((item) => item.id === row.frequencyId)?.name || '';
		row.medicationRoutesName = state.routeList.find((item) => item.id === row.medicationRoutesId)?.routeName || '';
	};

	// 根据天数，剂量 ，频次计算总量
	const calculateQuantity = (row: any) => {
		validateDrugDays(row);
		// 找到选中的频次项
		setMedicationRoutes(row);

		if (row.singleDose == 0 || row.medicationDays == 0 || row.frequencyId == null || row.frequencyId == 0) {
			return;
		}

		// 几个最小单位 "片"
		const unitQuantity = Math.ceil(row.singleDose / row.dosageValue);

		var frequency = state.frequencyList.find((item) => item.id === row.frequencyId);
		// 次数
		const executionFrequency = frequency?.executionFrequency ?? 0;
		if (executionFrequency == 0) {
			return;
		}
		var p = unitQuantity * executionFrequency * row.medicationDays;
		// 根据包装数量PackageQuantity 换算多少盒
		row.quantity = Math.ceil(p / row.outpatientPackageQuantity); // 除门诊包装数量

		// 计算金额
		row.amount = row.price * row.quantity;
	};

	// 组套
	const createPrescriptionGroup = () => {
		// 提取所有的 groupNo 值
		const groupNos = state.detailTableData.map((item) => item.groupNo ?? 0);

		// 将 detailSelectData 按字段 insertSort 正序排列 直接排序（Vue能检测到变化）
		state.detailSelectData.sort((a, b) => (a.insertSort ?? 0) - (b.insertSort ?? 0));

		// 找到 groupNo 的最大值
		const maxGroupNo = groupNos.length > 0 ? Math.max(...groupNos) : 0;

		// 新的 groupNo 应该是最大值加 1
		const newGroupNo = maxGroupNo + 1;

		//给 state.detailTableData 中的选中元素设置新的 groupNo
		state.detailSelectData.forEach((item) => {
			item.groupNo = newGroupNo;
			item.groupFlag = '|';
		});
		state.detailSelectData[0].groupFlag = '↱';
		state.detailSelectData[state.detailSelectData.length - 1].groupFlag = '↳';

		//遍历 state.detailTableData 取groupNo 不为0 的数据 按照 groupNo 分组 ，将只有一个元素的组组号设置为0

		const groupedData = state.detailTableData
			.filter((p) => p.groupNo != 0 && p.groupNo != null)
			.reduce((acc: any, item: any) => {
				if (!acc[item.groupNo]) {
					acc[item.groupNo] = [];
				}
				acc[item.groupNo].push(item);
				return acc;
			}, {});
		console.log(groupedData);
		for (const groupNo in groupedData) {
			if (groupedData[groupNo].length === 1) {
				groupedData[groupNo][0].groupNo = 0;
			}
		}
		state.detailTableData.forEach((item) => {
			if (item.groupNo == 0) {
				item.groupFlag = '';
			}
		});
	}; // 删除
	const delPrescriptionDetail = (row: any) => {
		if (row.id == null || row.id == 0) {
			state.detailTableData = state.detailTableData.filter((item) => item.drugCode !== row.drugCode);
			return;
		}

		ElMessageBox.confirm(`确定要删除吗?`, '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		})
			.then(async () => {
				await prescriptionApi.deleteDetail({ id: row.id });
				queryPrescriptionDetailList();
				ElMessage.success('删除成功');
			})
			.catch(() => {});
	};
	//

	// 选择处方类型后
	const prescTypeChange = (value: any) => {
		if (value == '2') {
			//中药处方
			state.isHerbPrescription = true;
			state.drugSearchParam.drugType = '3';
			state.drugSearchParam.drugTypeInclude = true;
			state.currentPrescription.isDecoction = true; // 是否代煎
		} else {
			state.isHerbPrescription = false;
			state.drugSearchParam.drugType = '1';
			state.drugSearchParam.drugTypeInclude = false;
			state.currentPrescription.isDecoction = false; // 是否代煎
		}
	};

	//选择诊断后
	const diagnosticCodeChange = (value: any, name: any) => {
		var obj = state.icd10Data.find((item) => {
			//数据源
			return item.code === value;
		});
		state.currentPrescription[name] = obj?.name ?? value;
	};

	// 选中处方列表后
	const onSelectPrescription = (val: any) => {
		if (val == null || val == undefined) {
			return;
		}
		state.currentPrescription = state.mainTableData.find((item) => item.id === val.key);
		// 手动触发 触发类型选中事件
		prescTypeChange(state.currentPrescription.outpatientPrescriptionType);
		queryPrescriptionDetailList();
	};

	return {
		initData,
		state,
		tcmDiagnosticRemoteMethod,
		tcmSyndromeCodeRemoteMethod,
		icd10RemoteMethod,
		checkDrugPermission, // 药品权限验证
		queryPrescriptionList, // 处方列表
		//    queryPrescriptionDetailList,// 处方明细表
		delPrescriptionDetail,
		savePrescription,
		addNew,
		calculateQuantity,
		setMedicationRoutes,
		createPrescriptionGroup,
		onSelectPrescription, // 选中处方列表后
		diagnosticCodeChange, // 诊断选中事件
		prescTypeChange, // 处方类型选中事件
	};
}

﻿<script lang="ts" name="refundApply" setup>
import { h, ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useRefundApplyApi } from '/@/api/outpatientDoctor/refundApply';
import { formatNumber } from '/@/utils/formatObject';
import { formatOnlyDate } from '/@/utils/formatTime';
import { useRegisterApi } from '/@/api/outpatientDoctor/register';
import { useOutpatientChargeApi } from '../../../../api/outpatientDoctor/charge';
import { SysUserApi, SysOrgApi } from '/@/api-services/api';
import { getAPI } from '/@/utils/axios-utils';
import { ElDivider } from 'element-plus'
//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);


const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	visitInfo: {} as any,
	tableLoading: false,
	selectData: [] as any[],
	tableData: [] as any[],
	refundApply: {} as any,

});
const outpatientChargeApi = useOutpatientChargeApi();
const registerApi = useRegisterApi();

const spacer = h(ElDivider, { direction: 'vertical' })
const refundApplyApi = useRefundApplyApi();
const ruleFormRef = ref();


// 自行添加其他规则
const rules = ref<FormRules>({
});

// 页面加载时
onMounted(async () => {
});


const queryTreatInfo = async () => {
	state.tableLoading = true;
	registerApi.page({ visitNo: state.refundApply.visitNo, pageSize: 1000 }).then(res => {
		state.visitInfo = (res.data.result?.items ?? [])[0] ?? {};
		state.tableLoading = false;
	}).catch(err => {
		state.tableLoading = false;
		// console.error('Failed to fetch patient info:', err);a
	})
};


const getListOfRefund = async () => {
	state.tableLoading = true;
	outpatientChargeApi.ListOfRefund({
		visitNo: state.refundApply.visitNo,
		isRefundApply: true,

	}).then(res => {
		// 添加到患者列表  state.patientList
		console.log('ListOfRefund', res.data.result);
		state.tableData = res.data.result;
		state.tableLoading = false;
	}).catch(err => {
		state.tableLoading = false;
		// console.error('Failed to fetch patient info:', err);a
	})
}

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.showDialog = true;
	state.title = title;
	row = row ?? {};
	state.refundApply = row;
	queryTreatInfo();
	getListOfRefund();
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await refundApplyApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};
const getUserName = (userId: any) => {
	if (!userId || !state.dropdownData.users) return '';
	const user = state.dropdownData.users.find((item: any) => item.value === userId);
	return user ? user.label : userId;
}
const getDeptName = (deptId: any) => {
	if (!deptId || !state.dropdownData.depts) return deptId;
	const dept = state.dropdownData.depts.find((item: any) => item.value === deptId);
	return dept ? dept.label : deptId;
}
const getAgeUnit = ((ageUnit: any) => {
	if (ageUnit === "0")
		return '岁'
	else if (ageUnit === "1")
		return '月'
	else if (ageUnit === "2")
		return '天'
	else return ''

});
//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="refundApply-container">
		<el-dialog v-model="state.showDialog" :style="{ width: '90%' }" :width="800" draggable
			:close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-card class="full-table" shadow="hover" :body-style="{ padding: '10px' }">

				<el-space :size="5" :spacer="spacer" v-if="state.visitInfo != null">

					<el-text class="mx-1" size="large">{{ state.visitInfo.patientName ?? "" }}</el-text>

					<el-text class="mx-1" size="small"><g-sys-dict v-model="state.visitInfo.sex"
							code="GenderEnum" /></el-text>

					<el-text><span style="margin-right: 5px;">{{ state.visitInfo.age
					}}{{ getAgeUnit(state.visitInfo.ageUnit) ?? '' }}</span> {{
								formatOnlyDate(state.visitInfo.birthday
									?? '') }} </el-text>
					<el-text> {{ state.visitInfo.deptName }}[{{ state.visitInfo.doctorName }} ]</el-text>
					<el-text>门诊号：{{ state.visitInfo.outpatientNo }} </el-text>
					<el-text>就诊号：{{ state.visitInfo.visitNo }} </el-text>
					<el-text>就诊时间：{{ state.visitInfo.outpatientNo }} </el-text>
					<el-text>卡号：{{ state.visitInfo.cardNo }} </el-text>
					<el-text> {{ state.visitInfo.feeName }} </el-text>

					<el-text> <el-tag type="danger">余额：0.00</el-tag> </el-text>
				</el-space>
				<!-- <el-form :model="state.visitQueryParams" ref="queryForm">

                        <el-form-item label="退费原因" style="margin-top: 20px">
                            <el-input placeholder="退费原因">
                            </el-input>
                        </el-form-item>
                    </el-form> -->
				<el-table stripe :data="state.tableData" style="margin-top: 20px;" tooltip-effect="light"
					default-expand-all v-loading="state.tableLoading" highlight-current-row row-key="id" border
					ref="tableRef">

					<el-table-column type="expand" label="展开" width="55" align="center" fixed="left">
						<template #default="props">




							<div style="margin-left: 150px;margin-bottom: 20px;">

								<el-table id="prescriptionTable" :data="props.row.details" border class="success-row"
									:class="{ 'success-row': true }" header-cell-class-name="sub-table-header"
									cell-class-name="sub-table-cell">
									<el-table-column prop='itemName' label='药品名称' show-overflow-tooltip width="140" />

									<el-table-column prop='spec' label='规格' show-overflow-tooltip />
									<el-table-column prop='number' label='数量' show-overflow-tooltip>
										<template #default="scope">
											<span>{{ scope.row.quantity }} {{ scope.row.unit }}</span>
										</template>
									</el-table-column>

									<el-table-column prop='price' label='价格' show-overflow-tooltip align="right">
										<template #default="scope">
											<span>{{ formatNumber(scope.row.price) }}</span>
										</template>
									</el-table-column>
									<el-table-column prop='amount' label='金额' show-overflow-tooltip align="right">
										<template #default="scope">
											<span>{{ formatNumber(scope.row.amount) }}</span>
										</template>
									</el-table-column>

									<el-table-column prop='manufacturer' label='生产厂家' show-overflow-tooltip
										width="140" />
									<el-table-column prop='medicineCode' label='国家医保编码' show-overflow-tooltip
										width="140" />
									<el-table-column prop='itemCode' label='药品编码' show-overflow-tooltip />

									<el-table-column prop='chargeCategoryCode' label='收费类别' show-overflow-tooltip
										width="140" />
								</el-table>
							</div>
						</template>
					</el-table-column>
					<el-table-column prop='billingNo' label='申请单号/处方号' show-overflow-tooltip width="140" />
					<el-table-column prop='billingType' label='类别' show-overflow-tooltip width="140" />
					<el-table-column prop='createTime' label='收费时间' show-overflow-tooltip width="140" />


					<el-table-column prop='billingTime' label='开单时间' show-overflow-tooltip width="140" />
					<el-table-column prop='billingDeptId' label='开单科室' show-overflow-tooltip>
						<template #default="scope">
							<span>{{ getDeptName(scope.row.billingDoctorId) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop='billingDoctorId' label='开单医生' show-overflow-tooltip>
						<template #default="scope">
							<span>{{ getUserName(scope.row.billingDoctorId) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop='executeDeptId' label='执行科室' show-overflow-tooltip>
						<template #default="scope">
							<span>{{ getDeptName(scope.row.billingDoctorId) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop='executeDoctorId' label='执行医生' show-overflow-tooltip>
						<template #default="scope">
							<span>{{ getUserName(scope.row.executeDoctorId) }}</span>
						</template>
					</el-table-column>
					<el-table-column prop='applyStatus' label='状态' show-overflow-tooltip width="140">
						<template #default="scope">
							<span v-if="scope.row.applyStatus === 0">已申请</span>

						</template>
					</el-table-column>

					<el-table-column prop='applyTime' label='操作时间' show-overflow-tooltip />
					<el-table-column prop='applyUserName' label='操作人' show-overflow-tooltip />

				</el-table>

			</el-card>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

/* 主表样式 */
:deep(.el-table) {
	background-color: #ffffff;

	// 主表头部样式
	.el-table__header-wrapper {
		th {
			background-color: #e4e7ed !important;
			color: #303133;
			font-weight: bold;
			border-bottom: 2px solid #dcdfe6;
		}
	}

	// 主表行样式
	.el-table__row {
		background-color: #f5f7fa;

		td {
			border-bottom: 1px solid #ebeef5;
		}
	}
}

/* 展开行内子表样式 */
:deep(.sub-table-header) {
	background-color: #dbecff !important;
	color: #2d5a9d !important;
	font-size: 13px;
	font-weight: 600;
	border-bottom: 2px solid #a0cfff;
}

:deep(.sub-table-cell) {
	background-color: #f0f7ff;
}

/* 展开行样式 */
:deep(.el-table__expanded-cell) {
	background-color: #ffffff;
	box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.08);
	padding: 25px !important;
}

/* 主表hover效果 */
:deep(.el-table__row:hover) {
	td {
		background-color: #ecf5ff !important;
	}
}

/* 表格边框和分割线 */
:deep(.el-table--border) {
	border: 1px solid #dcdfe6;

	&::after {
		background-color: #dcdfe6;
	}

	th.el-table__cell,
	td.el-table__cell {
		border-right: 1px solid #dcdfe6;
	}
}
</style>
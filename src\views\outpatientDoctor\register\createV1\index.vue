﻿<template>
	<div class="outpatientRegisterCreate-container" v-loading="state.loading">
		<div class="form-container">
			<!-- <el-card v-if="!isEdit" shadow="hover" :body-style="{ paddingBottom: '0' }">
				<div class="search-header">
					<span class="search-label">查询信息</span>
					<el-input v-model="state.idCardNo" placeholder="身份证号" @keyup.enter="getCardInfoByCardId" clearable
						style="width: 200px" />
					<el-button type="primary" @click="getCardInfoByCardId">查询</el-button>
					<el-button @click="resetForm">重置</el-button>
				</div>
			</el-card> -->
			<el-row>
				<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8">
					<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" inline-message :rules="rules">
						<el-card class="full-table" shadow="hover" style="margin-top: 5px;height:calc(100vh - 150px);">
							<template #header>
								<div class="card-header">
									<el-tag class="ml-2">{{ isEdit ? '编辑挂号信息' : '挂号信息' }}</el-tag>
								</div>
							</template>
							<el-row>
								<el-form-item v-show="false">
									<el-input v-model="state.ruleForm.id" />
								</el-form-item>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="卡号" prop="cardNo">
										<el-input v-model="state.ruleForm.cardNo" @keyup.enter="handleCardNoEnter" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="姓名" prop="patientName">
										<el-input v-model="state.ruleForm.patientName" placeholder="请输入姓名" clearable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="性别" prop="sex">
										<g-sys-dict v-model="state.ruleForm.sex" code="GenderEnum" render-as="select"
											placeholder="请选择性别" clearable filterable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="年龄" prop="age">
										<el-input v-model="state.ruleForm.age" type="number" placeholder="年龄"
											@input="handleCalculateBirthDate">
											<template #append>
												<g-sys-dict v-model="state.ruleForm.ageUnit" code="AgeUnit"
													@change="handleCalculateBirthDate" render-as="select"
													placeholder="岁" style="width: 52px" />
											</template>
										</el-input>
									</el-form-item>
								</el-col>
								<!-- <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" class="mb20">
							<el-form-item label="就诊类型" prop="visitType">
								<g-sys-dict v-model="state.ruleForm.visitType" code="VisitType" render-as="select"
									placeholder="请选择就诊类型" clearable filterable />
							</el-form-item>
						</el-col> -->
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="结算类别" prop="feeId">
										<PinyinSelect v-model="state.ruleForm.feeId" placeholder="请选择结算类别"
											:options="state.feeCategoryData" @change="feeCategoryChange" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" v-show="state.showRegTime"
									class="mb20">

									<el-form-item label="挂号时间" prop="consultationFee">
										<el-date-picker v-model="state.ruleForm.regTime" type="datetime"
											placeholder="挂号时间" format="YYYY/MM/DD hh:mm:ss" value-format="x" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="科室" prop="deptId">
										<PinyinSelect v-model="state.ruleForm.deptId" placeholder="请选择科室"
											@change="deptChange" :options="state.orgData" @click="loadOrgData" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="医生" prop="doctorId">
										<PinyinSelect v-model="state.ruleForm.doctorId" placeholder="请选择医生"
											@change="doctorChange" :options="state.userData" labelField="doctorName"
											valueField="doctorId" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="号别" prop="regCategoryId">
										<PinyinSelect v-model="state.ruleForm.regCategoryId" placeholder="请选择号别"
											@change="regCategoryChange" :options="state.regCategoryData" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="诊疗费" prop="consultationFee">
										<el-input v-model="state.ruleForm.consultationFee" disabled />
									</el-form-item>
								</el-col>


								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="证件类型" prop="cardType">
										<g-sys-dict v-model="state.ruleForm.cardType" code="CardTypeEnum"
											render-as="select" placeholder="请选择证件类型" clearable filterable
											@change="handleCardTypeChange" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="证件号" prop="idCardNo">
										<el-input v-model="state.ruleForm.idCardNo" placeholder="请输入证件号" show-word-limit
											clearable @blur="handleIdCardNoBlur" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="电话号码" prop="phone">
										<el-input v-model="state.ruleForm.phone" placeholder="请输入电话号码" show-word-limit
											clearable @blur="handleContactPhoneBlur" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="出生日期" prop="birthday">
										<el-date-picker v-model="state.ruleForm.birthday" type="date" placeholder="出生日期"
											@change="handleCalculateAgeAndUnit" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="民族" prop="nation">
										<g-sys-dict v-model="state.ruleForm.nation" code="NationEnum" render-as="select"
											placeholder="请选择民族" value-type="string" clearable filterable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="职业" prop="occupation">
										<g-sys-dict v-model="state.ruleForm.occupation" code="Occupation"
											render-as="select" placeholder="请选择职业" clearable filterable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="联系人" prop="contactName">
										<el-input v-model="state.ruleForm.contactName" placeholder="请输入联系人姓名"
											show-word-limit clearable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="联系电话" prop="contactPhone">
										<el-input v-model="state.ruleForm.contactPhone" placeholder="请输入联系人电话号码"
											show-word-limit clearable @blur="handleContactPhoneBlur" />
									</el-form-item>
								</el-col>
								<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="保险号码" prop="insuranceNum">
										<el-input v-model="state.ruleForm.insuranceNum" placeholder="请输入保险号码"
											show-word-limit clearable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="合同单位" prop="contractUnitId">
										<g-sys-dict v-model="state.ruleForm.contractUnitId" code="ContractUnit"
											render-as="select" placeholder="请选择合同单位" clearable filterable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
									<el-form-item label="医保卡余额" prop="medInsCardBalance">
										<el-input v-model="state.ruleForm.medInsCardBalance" disabled />
									</el-form-item>
								</el-col> -->
							</el-row>
							<el-row style="margin-top: auto" class="foot">
								<el-col :span="24">
									<el-card style="margin-top: 5px" :body-style="{ padding: '0px' }" shadow="never">
										<div class="button-container">
											<el-button-group>
												<el-button type="primary" v-if="!isEdit"
													:icon="isEdit ? 'ele-Edit' : 'ele-Plus'" @click="openAddMzRegister">
													{{ isEdit ? '保存' : '挂号' }}
												</el-button>
												<el-button type="success" icon="ele-Reading">读卡</el-button>
												<el-button type="info" icon="ele-Refresh"
													@click="resetForm">清屏</el-button>
												<el-button type="info" icon="ele-Printer">打印</el-button>
												<el-button type="warning" icon="ele-Back" @click="goBack"
													v-if="isEdit">返回</el-button>
											</el-button-group>
										</div>
									</el-card>
								</el-col>
							</el-row>
						</el-card>

					</el-form>
				</el-col>
				<el-col :span="16">

					<el-card class="full-table" shadow="hover" style="margin-top: 5px;height:calc(100vh - 150px);">
						<template #header>
							<div class="card-header">
								<el-tag class="ml-2">{{ isEdit ? '编辑挂号信息' : '挂号信息' }}</el-tag>
							</div>
						</template>
						<el-form :model="state.queryParams" ref="queryForm" label-width="auto" inline-message
							:rules="rules" :inline="true">
							<el-row>
								<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="mb5">
									<el-form-item label="门诊号">
										<el-input v-model="state.queryParams.id" clearable="" placeholder="请输入门诊号" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="mb5">
									<el-form-item label="患者姓名">
										<el-input v-model="state.queryParams.name" clearable="" placeholder="请输入患者姓名" />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="mb5">
									<el-form-item label="身份证号">
										<el-input v-model="state.queryParams.idCardNo" clearable=""
											placeholder="请输入身份证号" />
									</el-form-item> </el-col>
								<el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16" class="mb5">
									<el-form-item label="就诊日期">
										<el-date-picker v-model="state.dateTimeRange" type="datetimerange"
											range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间"
											:default-time="['2000-01-01 00:00:00', '2000-01-01 23:59:59']"
											value-format="YYYY-MM-DD HH:mm:ss" style="width: 380px"
											@change="handleDateTimeRangeChange" />
									</el-form-item> </el-col>
								<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8" class="mb5">
									<el-form-item>
										<el-button-group>
											<el-button type="primary" icon="ele-Search" @click="handleQuery"
												v-auth="'register:page'">
												查询
											</el-button>
											<el-button icon="ele-Refresh" @click="resetQuery"> 重置 </el-button>
										</el-button-group>
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
						<el-table :data="state.tableData" style="width: 100%" v-loading="state.loading"
							tooltip-effect="light" row-key="id" border="">
							<el-table-column fixed="left" prop="status" label="状态" show-overflow-tooltip="">
								<template #default="scope">
									<g-sys-dict v-model="scope.row.status" code="RegStatusEnum" />
								</template>
							</el-table-column>
							<el-table-column type="index" label="序号" width="55" align="center" />
							<el-table-column prop="outpatientNo" label="门诊号" show-overflow-tooltip="" />
							<el-table-column prop="patientName" label="患者姓名" show-overflow-tooltip="" />
							<el-table-column prop="sex" label="性别" show-overflow-tooltip="">
								<template #default="scope">
									<g-sys-dict v-model="scope.row.sex" code="GenderEnum" />
								</template>
							</el-table-column>
							<el-table-column prop="age" label="年龄" show-overflow-tooltip="" />
							<el-table-column prop="deptName" label="科室" show-overflow-tooltip="" />
							<el-table-column prop="doctorName" label="医生" show-overflow-tooltip="" />
							<el-table-column prop="feeName" label="费别" show-overflow-tooltip="" />
							<el-table-column prop="regCategory" label="号别" show-overflow-tooltip="" />

							<el-table-column prop="actualChargeFee" label="实收费用" show-overflow-tooltip="" />
							<!-- <el-table-column prop="cardNo" label="卡号" show-overflow-tooltip="" /> -->
							<el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
								<template #default="scope">
									<ModifyRecord :data="scope.row" />
								</template>
							</el-table-column>
							<el-table-column label="操作" width="200" align="center" fixed="right"
								show-overflow-tooltip="" v-if="auth('register:update') || auth('register:delete')">
								<template #default="scope">
									<el-button :disable="scope.row.status! = '9'" @click="charge(scope.row)"
										icon="ele-Edit" size="small" text="" type="primary" v-auth="'register:update'">
										收费
									</el-button>

									<el-button :disable="scope.row.status == '9'" icon="ele-Delete" size="small" text=""
										type="primary" @click="refundRegister(scope.row)" v-auth="'register:delete'"> 退号
									</el-button>
									<el-button :disable="scope.row.status! = '9'" icon="ele-Delete" size="small" text=""
										type="danger" @click="delRegister(scope.row)" v-auth="'register:delete'"> 删除
									</el-button>
								</template>
							</el-table-column>
						</el-table>
						<el-pagination v-model:currentPage="state.tableParams.page"
							v-model:page-size="state.tableParams.pageSize" :total="state.tableParams.total"
							:page-sizes="[10, 20, 50, 100, 200, 500]" size="small" background=""
							@size-change="handleSizeChange" @current-change="handleCurrentChange"
							layout="total, sizes, prev, pager, next, jumper" />
					</el-card>
				</el-col>

			</el-row>

		</div>

		<!-- <el-row style="margin-top: auto" class="foot">
			<el-col :span="24">
				<el-card style="margin-top: 5px" :body-style="{ padding: '0px' }" shadow="never">
					<div class="button-container">
						<el-button-group>
							<el-button type="primary" v-if="!isEdit" :icon="isEdit ? 'ele-Edit' : 'ele-Plus'"
								@click="openAddMzRegister">
								{{ isEdit ? '保存' : '挂号' }}
							</el-button>
							<el-button type="success" icon="ele-Reading">读卡</el-button>
							<el-button type="info" icon="ele-Refresh" @click="resetForm">清屏</el-button>
							<el-button type="info" icon="ele-Printer">打印</el-button>
							<el-button type="warning" icon="ele-Back" @click="goBack" v-if="isEdit">返回</el-button>
						</el-button-group>
					</div>
				</el-card>
			</el-col>
		</el-row> -->
		<SelfChargeDialog ref="selfChargeRef" :title="'自费支付'" @resetForm="resetForm" />
		<InsuranceChargeDialog ref="insuranceChargeRef" :title="'医保支付'" @resetForm="resetForm" />
		<InsuranceRegisterDialog ref="insuranceRegisterRef" :title="'医保登记'" @registerFinish="registerFinishHandler"
			@registerCancel="registerCancelHandler" />
		<InsurancePreSettlementDialog ref="insurancePreSettlementRef" :title="'医保预结算'" @resetForm="resetForm" />
	</div>
</template>

<script lang="ts" setup="" name="outpatientRegisterCreate">
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage, FormRules } from 'element-plus';
import SelfChargeDialog from '../create/component/selfChargeDialog.vue';
import InsuranceChargeDialog from '../create/component/insuranceChargeDialog.vue';
import InsuranceRegisterDialog from '../create/component/insuranceRegisterDialog.vue';
import InsurancePreSettlementDialog from '../create/component/insurancePreSettlementDialog.vue';
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
import { verifyPhone, verifyIdCard } from '/@/utils/toolsValidate';
import { useRoute } from 'vue-router';

import { useRegister } from '/@/composables/useRegister';
import { useRegisterApi } from '/@/api/outpatientDoctor/register';
import { useChargeApi } from '/@/api/outpatientDoctor/charge';


import { auth } from '/@/utils/authFunction';
import { formatDate } from '/@/utils/formatTime';
import ModifyRecord from '/@/components/table/modifyRecord.vue';
// 方式1：通过 index.ts 导入（推荐）
import {
	InsuranceRegister,
	SettleMzPreRequest,
} from '/@/models/insurance';
const register = useRegister();

const chargeApi = useChargeApi();


const registerApi = useRegisterApi();
const route = useRoute();

const selfChargeRef = ref<InstanceType<typeof SelfChargeDialog>>();
const insuranceChargeRef = ref<InstanceType<typeof InsuranceChargeDialog>>();
const insuranceRegisterRef = ref<InstanceType<typeof InsuranceRegisterDialog>>();
const insurancePreSettlementRef = ref<InstanceType<typeof InsurancePreSettlementDialog>>();

const ruleFormRef = ref();
// 获取路由参数，判断是新增还是编辑模式
const isEdit = route.query.type === 'edit';
const {
	initData,
	state,
	handleCardNoEnter,
	regCategoryChange,
	icd10RemoteMethod,
	loadOrgData,
	doctorChange, // 药品权限验证
	deptChange, // 处方列表 

	goBack,
	handleCalculateAgeAndUnit,
	handleCalculateBirthDate,
	handleCardTypeChange,
	handleIdCardNoBlur, // 选中处方列表后 
	handleContactPhoneBlur, // 处方类型选中事件
} = useRegister();
onMounted(async () => {
	initData();
});
// 重置表单
const resetForm = () => {

	ruleFormRef.value?.resetFields();
	state.idCardNo = '';

	// 重置后设置默认值
	state.ruleForm.ageUnit = '0';
	state.ruleForm.cardType = 0;
};
const registerFinishHandler = async (settleMzPreRequest: SettleMzPreRequest) => {
	// 处理医保登记完成后的逻辑
	//ElMessage.success('医保登记成功'); 
	//保存挂号记录


	insurancePreSettlementRef.value?.openDialog(state.ruleForm, settleMzPreRequest);
	// if (state.ruleForm.registerId === 0 || state.ruleForm.registerId === null) {
	// 	// 新增挂号记录
	// 	state.loading = true;
	// 	await registerApi.add(state.ruleForm).then((res) => {
	// 		if (res.data.type == 'success') {
	// 			debugger;
	// 			state.ruleForm.registerId = res.data.result;
	// 			insurancePreSettlementRef.value?.openDialog(state.ruleForm, insuranceRegister);
	// 		}
	// 	}).finally(() => {
	// 		state.loading = false;
	// 	});

	// } else {
	// 	insurancePreSettlementRef.value?.openDialog(state.ruleForm, insuranceRegister);
	// }

};


const charge = (row: any) => {
	// 根据挂号状态判断是否可以收费
	if (row.status === '9') {
		setFormData(row);

		insuranceRegisterRef.value?.openDialog(state.ruleForm);
	} else {
		ElMessage.error('当前挂号状态不允许收费');
	}
};
const registerCancelHandler = () => {
	// 处理医保登记取消后的逻辑
	ElMessage.success('医保登记取消');

};
const rules = ref<FormRules>({
	regCategoryId: [{ required: true, message: '请选择号别！', trigger: 'change' }],
	name: [{ required: true, message: '请输入患者姓名！', trigger: 'blur' }],
	sex: [{ required: true, message: '请输入性别！', trigger: 'blur' }],
	age: [{ required: true, message: '请输入年龄！', trigger: 'blur' }],
	feeId: [{ required: true, message: '请选择费别！', trigger: 'change' }],
	deptId: [{ required: true, message: '请选择科室！', trigger: 'change' }],
	doctorId: [{ required: true, message: '请选择医生！', trigger: 'change' }],
	contactPhone: [
		{
			validator: (rule, value, callback) => {
				if (value && !verifyPhone(value)) {
					callback(new Error('请输入正确的手机号码'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
	idCardNo: [
		{
			validator: (rule, value, callback) => {
				// 只有当证件类型为身份证(0)时才进行验证
				if (value && state.ruleForm.cardType === 0 && !verifyIdCard(value)) {
					callback(new Error('请输入正确的身份证号码'));
				} else {
					callback();
				}
			},
			trigger: 'blur',
		},
	],
});
const feeCategoryChange = (value: string) => {
	debugger
	state.currentfeeCategory = state.feeCategoryData.filter((item: any) => item.id === value)[0];
	console.log('feeCategoryChange', state.currentfeeCategory);
	state.ruleForm.insurance = {
		medicalPoolingCategory: state.currentfeeCategory.medicalPoolingCategory,//医疗统筹类别
		medicalInsuranceFlag: state.currentfeeCategory.medicalInsuranceFlag, //险种标志

	}

};
const setFormData = (row: any) => {
	state.ruleForm = row;
	state.ruleForm.registerId = row.id;
	state.currentfeeCategory = state.feeCategoryData.filter((item: any) => item.id === row.feeId)[0];

	state.ruleForm.insurance = {
		medicalPoolingCategory: state.currentfeeCategory.medicalPoolingCategory,//医疗统筹类别
		medicalInsuranceFlag: state.currentfeeCategory.medicalInsuranceFlag, //险种标志

	}
};
const openAddMzRegister = () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			// if (state.isEdit) {
			// 	// 编辑模式，直接保存
			// 	try {
			// 		state.loading = true;
			// 		await registerApi.update(state.ruleForm);
			// 		ElMessage.success('保存成功');
			// 		// 返回列表页
			// 		goBack();
			// 	} catch (error) {
			// 		console.error('保存失败:', error);
			// 		ElMessage.error('保存失败');
			// 	} finally {
			// 		state.loading = false;
			// 	}
			// } else {
			// 新增模式，打开收费对话框

			if (state.currentfeeCategory.medCategory === 1)//医保			//selfChargeRef.value?.openDialog(state.ruleForm);
			{
				// 1 如果通过读卡或医保二维码获取患者信息，将值转递给医保登记对话框医保登记对话框
				// 2 保存挂号记录 暂不结算
				// 3 将结算id // 获取结算费用 
				state.loading = true;
				await registerApi.add(state.ruleForm).then(async (res) => {
					if (res.data.type == 'success') {
						handleQuery();
						state.ruleForm.registerId = res.data.result;
						state.ruleForm.insurance = {
							medicalPoolingCategory: state.currentfeeCategory.medicalPoolingCategory,//医疗统筹类别
							medicalInsuranceFlag: state.currentfeeCategory.medicalInsuranceFlag, //险种标志
						}
						debugger;
						const registerDetail = await registerApi.detail(state.ruleForm.registerId);
						//const chargeList = await chargeApi.GetChargeList([registerDetail.data.result.chargeMainId])


						const registerParams: InsuranceRegister = {
							idCardNo: state.ruleForm.idCardNo,
							name: state.ruleForm.patientName,
							insurance: state.ruleForm.insurance,
							registerId: state.ruleForm.registerId,
							chargeList: [],// chargeList.data.result,
							diagList: [],
							outpatientNo: state.ruleForm.outpatientNo,
							deptId: state.ruleForm.deptId,
							chargeId: registerDetail.data.result.chargeMainId

						};
						insuranceRegisterRef.value?.openDialog(registerParams);
					}
				}).catch((error) => {
					ElMessage.error(`挂号失败: ${error.message}`);

				})

					.finally(() => {
						state.loading = false;
					});
			} else {
				//自费
				selfChargeRef.value?.openDialog(state.ruleForm);
			}
			//}
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};


// 处理日期时间范围变化
const handleDateTimeRangeChange = (val: any) => {
	if (val && val.length === 2) {
		state.queryParams.startTime = val[0];
		state.queryParams.endTime = val[1];
	} else {
		state.queryParams.startTime = undefined;
		state.queryParams.endTime = undefined;
	}
};
// 页面加载时
onMounted(async () => {
	// 设置当天日期作为默认就诊日期范围
	const today = new Date();
	const startOfDay = new Date(today);
	startOfDay.setHours(0, 0, 0, 0);
	const endOfDay = new Date(today);
	endOfDay.setHours(23, 59, 59, 999);

	// 格式化日期
	const startTimeStr = formatDate(startOfDay, 'YYYY-mm-dd HH:MM:SS');
	const endTimeStr = formatDate(endOfDay, 'YYYY-mm-dd HH:MM:SS');

	// 设置日期范围
	state.dateTimeRange = [startTimeStr, endTimeStr];
	state.queryParams.startTime = startTimeStr;
	state.queryParams.endTime = endTimeStr;

	await handleQuery();
});

// 查询操作
const handleQuery = async () => {
	state.loading = true;
	var res = await registerApi.page(Object.assign(state.queryParams, state.tableParams));
	state.tableData = res.data.result?.items ?? [];
	state.tableParams.total = res.data.result?.total;
	state.loading = false;
};
// 重置操作
const resetQuery = () => {
	state.queryParams.id = undefined;
	state.queryParams.name = undefined;
	state.queryParams.idCardNo = undefined;

	// 重新设置当天日期作为默认就诊日期范围
	const today = new Date();
	const startOfDay = new Date(today);
	startOfDay.setHours(0, 0, 0, 0);
	const endOfDay = new Date(today);
	endOfDay.setHours(23, 59, 59, 999);

	// 格式化日期 - 使用大写的 MM 和 DD 以及 HH:mm:ss 格式
	const startTimeStr = formatDate(startOfDay, 'YYYY-mm-dd HH:MM:SS');
	const endTimeStr = formatDate(endOfDay, 'YYYY-mm-dd HH:MM:SS');

	// 设置日期范围
	state.dateTimeRange = [startTimeStr, endTimeStr];
	state.queryParams.startTime = startTimeStr;
	state.queryParams.endTime = endTimeStr;

	handleQuery();
};


//退号操作
const refundRegister = (row: any) => {
	ElMessageBox.confirm(`确定要退号吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await registerApi.refund({ id: row.id });
			handleQuery();
			ElMessage.success('退号成功');
		})
		.catch(() => { });
};
const delRegister = (row: any) => {
	ElMessageBox.confirm(`确定要删除挂号信息吗？`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await registerApi.delete({ id: row.id });
			handleQuery();
			ElMessage.success('删除成功');
		})
		.catch(() => { });
};

// 改变页面容量
const handleSizeChange = (val: number) => {
	state.tableParams.pageSize = val;
	handleQuery();
};

// 改变页码序号
const handleCurrentChange = (val: number) => {
	state.tableParams.page = val;
	handleQuery();
};
</script>
<style scoped>
.outpatientRegisterCreate-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	/* 使容器占满整个视口高度 */
}

.form-container {
	flex: 1;
	/* 表单容器占据剩余空间 */
	overflow-y: auto;
	/* 增加垂直滚动条 */
	padding: 0px;
	/* 可选：添加内边距 */
}

.foot {
	position: sticky;
	bottom: 0;
	background-color: white;
	/* 背景色防止内容穿透 */
	z-index: 1000;
	/* 确保底部内容在最上层 */
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}

:deep(.el-card__header) {
	padding: 5px;
}

:deep(.el-card__body) {
	padding: 10px;
}

.button-container {
	display: flex;
	justify-content: flex-end;
	padding: 10px;
	/* 可选：添加一些内边距 */
}

.mb20 {
	margin-bottom: 5px;
}

.search-header {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 10px;
}

.search-label {
	font-weight: bold;
	min-width: 70px;
}
</style>

/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { BusinessTypeEnum } from './business-type-enum';
import { Filter } from './filter';
import { Search } from './search';
 /**
 * 就诊卡管理分页查询输入参数
 *
 * @export
 * @interface CardInfoInput
 */
export interface CardInfoInput {

    /**
     * @type {Search}
     * @memberof CardInfoInput
     */
    search?: Search;

    /**
     * 模糊查询关键字
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    keyword?: string | null;

    /**
     * @type {Filter}
     * @memberof CardInfoInput
     */
    filter?: Filter;

    /**
     * 当前页码
     *
     * @type {number}
     * @memberof CardInfoInput
     */
    page?: number;

    /**
     * 页码容量
     *
     * @type {number}
     * @memberof CardInfoInput
     */
    pageSize?: number;

    /**
     * 排序字段
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    field?: string | null;

    /**
     * 排序方向
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    order?: string | null;

    /**
     * 降序排序
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    descStr?: string | null;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    cardNo?: string | null;

    /**
     * @type {BusinessTypeEnum}
     * @memberof CardInfoInput
     */
    businessType?: BusinessTypeEnum;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    name?: string | null;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof CardInfoInput
     */
    idCardNum?: string | null;

    /**
     * 开始时间
     *
     * @type {Date}
     * @memberof CardInfoInput
     */
    startTime?: Date | null;

    /**
     * 结束时间
     *
     * @type {Date}
     * @memberof CardInfoInput
     */
    endTime?: Date | null;
}

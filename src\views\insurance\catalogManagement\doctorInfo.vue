<script lang="ts" setup name="doctorInfo">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCatalogManagementApi } from '/@/api/insurance/catalogManagement';
import type { DoctorInfo } from '/@/api/insurance/catalogManagement';

const catalogApi = useCatalogManagementApi();
const editDialogRef = ref();

const state = reactive({
	tableLoading: false,
	showAdvanceQueryUI: false,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'lastSyncTime',
		order: 'descending',
	},
	tableData: [] as DoctorInfo[],
	departments: [
		{ code: '001', name: '内科' },
		{ code: '002', name: '外科' },
		{ code: '003', name: '妇产科' },
		{ code: '004', name: '儿科' },
		{ code: '005', name: '眼科' },
		{ code: '006', name: '耳鼻喉科' },
		{ code: '007', name: '口腔科' },
		{ code: '008', name: '皮肤科' },
		{ code: '009', name: '急诊科' },
		{ code: '010', name: '麻醉科' },
	],
});

// 页面加载时
onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	try {
		const result = await catalogApi.getLocalDoctorInfo(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
		state.tableParams.total = result?.total || 0;
		state.tableData = result?.items ?? [];

		// 添加科室名称
		state.tableData.forEach((item) => {
			const dept = state.departments.find((d) => d.code === item.ksBm);
			(item as any).departmentName = dept?.name || item.ksBm;
		});
	} catch (error) {
	} finally {
		state.tableLoading = false;
	}
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 删除
const delDoctor = (row: any) => {
	ElMessageBox.confirm(`确定要删除医师"${row.ysXm}"吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			ElMessage.warning('删除功能暂未实现');
		})
		.catch(() => {});
};

// 批量删除
const batchDelDoctor = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			ElMessage.warning('批量删除功能暂未实现');
		})
		.catch(() => {});
};

// 同步医师信息
const handleSync = async () => {
	try {
		const request = {
			ysBm: '',
			ysXm: '',
			ksBm: '',
		};
		const response = await catalogApi.queryDoctorInfo(request);
		if (response.data?.result) {
			ElMessage.success('同步完成');
			await handleQuery();
		}
	} catch (error) {}
};

// 获取科室名称
const getDepartmentName = (code?: string) => {
	if (!code) return '-';
	const dept = state.departments.find((d) => d.code === code);
	return dept?.name || code;
};

// 格式化日期
const formatDate = (dateStr?: string) => {
	if (!dateStr) return '-';
	return new Date(dateStr).toLocaleString('zh-CN');
};
</script>

<template>
	<div class="doctor-info-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键词">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="医师编码、姓名" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="科室">
							<el-select v-model="state.tableQueryParams.ksBm" placeholder="请选择科室" clearable>
								<el-option v-for="dept in state.departments" :key="dept.code" :label="dept.name" :value="dept.code" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelDoctor" :disabled="state.selectData.length == 0"> 删除 </el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef?.openDialog(null, '新增医师')"> 新增 </el-button>
								<el-button type="success" style="margin-left: 5px" icon="ele-Refresh" @click="handleSync"> 同步医师信息 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center"  />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="ysBm" label="医师编码" width="120" fixed="left" show-overflow-tooltip />
				<el-table-column prop="ysXm" label="医师姓名" width="120" show-overflow-tooltip />
				<el-table-column prop="ksBm" label="科室编码" width="120" show-overflow-tooltip />
				<el-table-column prop="departmentName" label="科室名称" width="150" show-overflow-tooltip />
				<el-table-column prop="lastSyncTime" label="最后同步时间" width="160" sortable="custom">
					<template #default="{ row }">
						<span>{{ formatDate(row.lastSyncTime) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150" align="center" fixed="right" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef?.openDialog(scope.row, '编辑医师')"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delDoctor(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
		</el-card>
	</div>
</template>

<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>

<script lang="ts" setup name="hospitalItems">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useCatalogManagementApi } from '/@/api/insurance/catalogManagement';
import type { HospitalItemCatalog } from '/@/api/insurance/catalogManagement';

const catalogApi = useCatalogManagementApi();
const editDialogRef = ref();

const state = reactive({
	exportLoading: false,
	tableLoading: false,
	syncLoading: false,
	incrementalSyncLoading: false,
	showAdvanceQueryUI: false,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'lastSyncTime',
		order: 'descending',
		descStr: 'descending',
	},
	tableData: [] as HospitalItemCatalog[],
});

// 页面加载时
onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	try {
		const result = await catalogApi.getLocalHospitalItems(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
		state.tableParams.total = result?.total || 0;
		state.tableData = result?.items ?? [];
	} catch (error) {
		ElMessage.error('获取数据失败');
	} finally {
		state.tableLoading = false;
	}
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 删除
const delHospitalItem = (row: any) => {
	ElMessageBox.confirm(`确定要删除项目"${row.yyxmmc}"吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			ElMessage.warning('删除功能暂未实现');
		})
		.catch(() => {});
};

// 批量删除
const batchDelHospitalItem = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			ElMessage.warning('批量删除功能暂未实现');
		})
		.catch(() => {});
};

// 同步目录
const handleSync = async () => {
	try {
		state.syncLoading = true;
		const response = await catalogApi.syncHospitalItems({ p_filetype: 'json' });
		if (response.data?.result) {
			ElMessage.success('同步完成');
			await handleQuery();
		}
	} catch (error) {
		ElMessage.error('同步失败');
	} finally {
		state.syncLoading = false;
	}
};

// 增量同步
const handleIncrementalSync = async () => {
	try {
		state.incrementalSyncLoading = true;
		const response = await catalogApi.incrementalSyncHospitalItems();
		if (response.data?.result) {
			ElMessage.success('增量同步完成');
			await handleQuery();
		}
	} catch (error) {
		ElMessage.error('增量同步失败');
	} finally {
		state.incrementalSyncLoading = false;
	}
};

// 导出数据
const exportHospitalItemCommand = async (command: string) => {
	try {
		state.exportLoading = true;
		if (command === 'select') {
			ElMessage.info('导出选中功能开发中...');
		} else if (command === 'current') {
			ElMessage.info('导出本页功能开发中...');
		} else if (command === 'all') {
			ElMessage.info('导出全部功能开发中...');
		}
	} finally {
		state.exportLoading = false;
	}
};

// 格式化货币
const formatCurrency = (value: number) => {
	if (value === null || value === undefined) return '-';
	return `¥${value.toFixed(2)}`;
};

// 格式化日期
const formatDate = (dateStr: string) => {
	if (!dateStr) return '-';
	return new Date(dateStr).toLocaleString('zh-CN');
};
</script>

<template>
	<div class="hospital-items-container" v-loading="state.exportLoading">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键词">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="项目编码、名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="药品标志">
							<el-select v-model="state.tableQueryParams.ypBz" placeholder="请选择" clearable>
								<el-option label="药品" value="1" />
								<el-option label="非药品" value="0" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="险种标志">
							<el-select v-model="state.tableQueryParams.xzBz" placeholder="请选择" clearable>
								<el-option label="职工医保" value="310" />
								<el-option label="居民医保" value="390" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="人群类别">
							<el-select v-model="state.tableQueryParams.rqLb" placeholder="请选择" clearable>
								<el-option label="普通人群" value="1" />
								<el-option label="特殊人群" value="2" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelHospitalItem" :disabled="state.selectData.length == 0"> 删除 </el-button>
								<el-button type="primary" style="margin-left: 5px" icon="ele-Plus" @click="editDialogRef?.openDialog(null, '新增医院项目')"> 新增 </el-button>
								<el-button type="success" style="margin-left: 5px" icon="ele-Refresh" @click="handleSync" :loading="state.syncLoading"> 同步目录 </el-button>
								<el-button type="warning" style="margin-left: 5px" icon="ele-Download" @click="handleIncrementalSync" :loading="state.incrementalSyncLoading"> 增量同步 </el-button>
								<el-dropdown :show-timeout="70" :hide-timeout="50" @command="exportHospitalItemCommand">
									<el-button type="primary" style="margin-left: 5px" icon="ele-FolderOpened" v-reclick="20000"> 导出 </el-button>
									<template #dropdown>
										<el-dropdown-menu>
											<el-dropdown-item command="select" :disabled="state.selectData.length == 0">导出选中</el-dropdown-item>
											<el-dropdown-item command="current">导出本页</el-dropdown-item>
											<el-dropdown-item command="all">导出全部</el-dropdown-item>
										</el-dropdown-menu>
									</template>
								</el-dropdown>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="yyxmbm" label="项目编码" width="120" fixed="left" show-overflow-tooltip />
				<el-table-column prop="yyxmmc" label="项目名称" width="200" show-overflow-tooltip />
				<el-table-column prop="ylxmbm" label="医疗项目编码" width="120" show-overflow-tooltip />
				<el-table-column prop="gg" label="规格" width="100" show-overflow-tooltip />
				<el-table-column prop="dw" label="单位" width="80" />
				<el-table-column prop="dj" label="单价" width="100" align="right">
					<template #default="{ row }">
						<span>{{ formatCurrency(row.dj) }}</span>
					</template>
				</el-table-column>
				<el-table-column prop="zfbl" label="自付比例" width="100" align="right">
					<template #default="{ row }">
						<span v-if="row.zfbl">{{ (row.zfbl * 100).toFixed(2) }}%</span>
						<span v-else>-</span>
					</template>
				</el-table-column>
				<el-table-column prop="ypbz" label="药品标志" width="100" align="center">
					<template #default="{ row }">
						<el-tag :type="row.ypbz === '1' ? 'success' : 'info'" size="small">
							{{ row.ypbz === '1' ? '药品' : '非药品' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="xzbz" label="险种标志" width="100" />
				<el-table-column prop="lastSyncTime" label="最后同步时间" width="160" sortable="custom">
					<template #default="{ row }">
						<span>{{ formatDate(row.lastSyncTime) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" width="150" align="center" fixed="right" show-overflow-tooltip>
					<template #default="scope">
						<el-button icon="ele-Edit" size="small" text type="primary" @click="editDialogRef?.openDialog(scope.row, '编辑医院项目')"> 编辑 </el-button>
						<el-button icon="ele-Delete" size="small" text type="primary" @click="delHospitalItem(scope.row)"> 删除 </el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
		</el-card>
	</div>
</template>

<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>

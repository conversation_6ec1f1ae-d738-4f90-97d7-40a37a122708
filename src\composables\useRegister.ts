import { ref, reactive, onMounted } from 'vue';
import { ElMessage, FormRules } from 'element-plus';
import EditCharge from '../views/outpatientDoctor/register/create/component/selfChargeDialog.vue';
import PinyinSelect from '/@/components/pinyinSelect/index.vue';
import { calculateAgeAndUnit, calculateBirthDate, extractInfoFromIdCard, verifyPhone, verifyIdCard } from '/@/utils/toolsValidate';
import { useRouter, useRoute } from 'vue-router';
import mittBus from '/@/utils/mitt';

import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { useSchedulingPlanApi } from '/@/api/registration/schedulingPlan';
import { useMedicalCardInfoApi } from '/@/api/patient/medicalCardInfo';
import { useRegisterApi } from '/@/api/outpatientDoctor/register';
 import { useBaseApi } from '/@/api/base';

const insurancePatientApi = useBaseApi("insurancePatient");
// API服务
const basicInfoApi = useBasicInfoApi();
const medicalCardInfoApi = useMedicalCardInfoApi();
const schedulingPlanApi = useSchedulingPlanApi();
const registerApi = useRegisterApi();
const route = useRoute();

const editChargeRef = ref<InstanceType<typeof EditCharge>>();

const ruleFormRef = ref();
 
const state = reactive({
	showDialog : false,
	title :'',
	loading: false,
	icd10Loading: false,
	idCardNo: '',
	ruleForm: {} as any, //挂号信息
	feeCategoryData: [] as any, //费别数据
	currentfeeCategory: {} as any, //当前选中的结算类别
	orgData: [] as any, //科室数据
	userData: [] as any, // 排班计划数据
	allRegCategoryData: [] as any, //号别数据
	regCategoryData: [] as any, //号别数据
	icd10Data: [] as any, //icd10数据
	schedulingParams: {} as any, // 排班参数
	showRegTime: false,
	isEdit:true,
	registerId:0,
	 
	tableData: [] as any,
	dateTimeRange: [] as any, // 日期时间范围，使用 any 类型避免类型错误
	queryParams: {
		id: undefined,
		name: undefined,
		idCardNo: undefined,
		startTime: undefined as string | undefined,
		endTime: undefined as string | undefined,
	},
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0 as any,
	},
});
 
export function useRegister() {

	const initData = async () => { 
		// 获取路由参数，判断是新增还是编辑模式
		state.isEdit= route?.query?.type==='edit' ;
		state.registerId= route?.query.id ? Number(route.query.id) : 0;
		state.loading = true;
	try {
		// 获取基础数据
		var res = await basicInfoApi.getFeeCategories({ usageScope: 1 });
		state.feeCategoryData = res.data.result ?? [];
		if (state.isEdit) {

			var res1 = await basicInfoApi.getDepartments(["701"]);
			state.orgData = res1.data.result ?? [];
			var res2 = await basicInfoApi.getRegCategories({});
			state.regCategoryData = res2.data.result ?? [];
			var res3 = await basicInfoApi.getUsers({});
			state.userData = (res3.data.result ?? [])
				.map((item: any) => {
					return {
						doctorName: item.realName,
						doctorId: item.id,
					};
				})
				;
			console.log(state.userData);
		} else {

			var res1 = await schedulingPlanApi.getSchedulingDept({});
			state.orgData = res1.data.result ?? [];
			var res2 = await basicInfoApi.getRegCategories({});
			state.allRegCategoryData = res2.data.result ?? [];
		}


		// 如果是编辑模式，获取挂号详情
		// if (state.isEdit && state.registerId) {
		// 	const detailRes = await registerApi.detail(state.registerId);
		// 	if (detailRes.data.result) {
		// 		state.ruleForm = detailRes.data.result;
		// 		console.log(state.ruleForm);
		// 		// 如果有科室ID，获取医生列表
		// 		if (state.ruleForm.deptId) {
		// 			await deptChange(state.ruleForm.deptId);
		// 		}
		// 	}
		// } else {
			// 设置默认值
			// 设置默认年龄单位为"岁"
			if (!state.ruleForm.ageUnit) {
				state.ruleForm.ageUnit = '0';
			}
			// 设置默认证件类型为身份证(0)
			if (state.ruleForm.cardType === undefined) {
				state.ruleForm.cardType = 0;
			}
		//}
	} catch (error) {
		console.error('初始化数据失败:', error);
		ElMessage.error('初始化数据失败');
	} finally {
		state.loading = false;
	}
	};
const loadOrgData = async () => {
	state.loading = true;
	try {
		const res1 = await schedulingPlanApi.getSchedulingDept({});
		state.orgData = res1.data.result ?? [];
	} catch (error) {
		console.error('获取科室数据失败:', error);
		ElMessage.error('获取科室数据失败');
	} finally {
		state.loading = false;
	}
};
//卡号回车
const handleCardNoEnter = () => {

	console.log('回车键被按下' + state.ruleForm.cardNo.length);
	if (state.ruleForm.cardNo.length == 18) {
		// 身份证号 
		getCardInfoByCardId(state.ruleForm.cardNo, "")
	}
	else if (state.ruleForm.cardNo.length == 8) {
		// 就诊卡号
		getCardInfoByCardId("", state.ruleForm.cardNo)
	}
	else if (state.ruleForm.cardNo.length == 8) {
		// 健康聊城
		//getCardInfoByCardId(state.ruleForm.cardNo)
	}
	else if (state.ruleForm.cardNo.length == 8) {
		// 医保电子凭证
		//getCardInfoByCardId(state.ruleForm.cardNo)
	}
	// 查询医保身份信息
	queryBasicInfo();

};
//自行添加其他规则

//根据身份证号查询卡信息
const getCardInfoByCardId = async (idCardNo: string, cardNo: string) => {
	// 检查身份证号码是否已输入
	//if (!state.idCardNo) return;

	// 异步获取身份证号码对应的卡片信息
	var res = await medicalCardInfoApi.cardInfoByIdOrCardNo({ idCardNo: idCardNo, cardNo: cardNo });
   
	console.log(res);
	// 合并当前表单信息与获取的卡片信息
	var cardInfo = Object.assign(state.ruleForm, res.data.result);

	// 更新表单信息
	state.ruleForm = cardInfo;

	// 更新表单中的患者ID和卡片ID
	state.ruleForm.patientId = res.data.result?.patientId;
	state.ruleForm.cardId = res.data.result?.id;

	// 初始化或重置表单中的其他字段
	state.ruleForm.id = 0;
	state.ruleForm.createTime = undefined;
	state.ruleForm.createUserId = undefined;
	state.ruleForm.createUserName = undefined;
	state.ruleForm.updateTime = undefined;
	state.ruleForm.updateUserId = undefined;
	state.ruleForm.updateUserName = undefined;

	// 如果有出生日期，计算年龄
	if (state.ruleForm.birthday) {
		handleCalculateAgeAndUnit();
	}
	// 如果有年龄但没有出生日期，计算出生日期
	else if (state.ruleForm.age && !state.ruleForm.birthday) {
		handleCalculateBirthDate();
	}
};
const icd10RemoteMethod = async (params: any) => {
	state.icd10Data = await basicInfoApi.getIcd10s({ keyword: params }).then((res) => res?.data?.result ?? {});
};

// 选中号别change事件
const regCategoryChange = (value: number) => {
	var regCategory = state.regCategoryData.find((item: any) => item.id == value);
	state.ruleForm.registrationFee = 0;
	state.ruleForm.consultationFee = regCategory?.consultationFee ?? 0;
};
// 选中医生change事件
const doctorChange = async (value: number) => {
	if (state.isEdit) return;
	state.schedulingParams.doctorId = value;

	// var timePeriod = state.userData.find((item: any) => item.id == value);
	// state.ruleForm.timePeriodId = timePeriod?.id;
	// 获取挂号类别数据
	const res = await schedulingPlanApi.getSchedulingDoctorRegCategory(state.schedulingParams);
	// 过滤  

	state.ruleForm.regCategoryId = null;
	if (res.data.result && Array.isArray(res.data.result) && res.data.result.length > 0) {
		const regCategories = res.data.result ?? []
		state.regCategoryData = state.allRegCategoryData.filter((item: any) =>
			regCategories.some((category: any) => category.regCategoryId === item.id)
		);

	} else {

	}
};

// 选中科室change事件
const deptChange = async (deptId: number) => {
	if (state.isEdit) return;
	state.schedulingParams = {};
	state.schedulingParams.deptId = deptId;
	state.ruleForm.doctorId = null;
	const res = await schedulingPlanApi.getSchedulingDoctor(state.schedulingParams);
	if (res.data.result && Array.isArray(res.data.result) && res.data.result.length > 0) {
		state.userData = res.data.result ?? [];

	} else {

	}

};
 


// 关闭当前标签页
const goBack = () => {
	mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 1, ...route }));
};



// 根据出生日期计算年龄和年龄单位
const handleCalculateAgeAndUnit = () => {
	if (!state.ruleForm.birthday) return;

	const result = calculateAgeAndUnit(state.ruleForm.birthday);
	state.ruleForm.age = result.age;
	state.ruleForm.ageUnit = result.ageUnit;
};

// 根据年龄和年龄单位计算出生日期
const handleCalculateBirthDate = () => {
	if (!state.ruleForm.age || state.ruleForm.age <= 0) return;

	state.ruleForm.birthday = calculateBirthDate(state.ruleForm.age, state.ruleForm.ageUnit);
};

// 证件类型变更事件
const handleCardTypeChange = () => {
	if (state.ruleForm.idCardNo) {
		if (state.ruleForm.cardType === 0) {
			handleExtractInfoFromIdCard();
		}
		// 无论证件类型是什么，都触发证件号的验证
		ruleFormRef.value.validateField('idCardNo');
	}
};

// 证件号失焦事件
const handleIdCardNoBlur = () => {
	if (state.ruleForm.idCardNo) {
		// 如果是身份证，提取信息
		if (state.ruleForm.cardType === 0) {
			handleExtractInfoFromIdCard();
		}
		// 触发证件号的验证
		ruleFormRef.value.validateField('idCardNo');
	}
};

// 从身份证号中提取信息
const handleExtractInfoFromIdCard = () => {
	const result = extractInfoFromIdCard(state.ruleForm.idCardNo);
	if (result) {
		state.ruleForm.birthday = result.birthday;
		state.ruleForm.sex = result.sex;
		handleCalculateAgeAndUnit();
	}
};

// 联系人电话号码失焦事件
const handleContactPhoneBlur = () => {
	if (state.ruleForm.contactPhone) {
		// 使用表单验证方式显示错误，触发表单项的验证
		ruleFormRef.value.validateField('contactPhone');
	}
};

// 查询医保信息 
const queryBasicInfo = async () => {
console.log(state.ruleForm);


};
return {
	initData,
	state,
	handleCardNoEnter, 
	regCategoryChange,
	icd10RemoteMethod,
	loadOrgData,
	doctorChange, // 药品权限验证
	deptChange, // 处方列表 
 
	goBack,
 
	handleCalculateAgeAndUnit,
	handleCalculateBirthDate,
	handleCardTypeChange,
	handleIdCardNoBlur, // 选中处方列表后 
	handleContactPhoneBlur, // 处方类型选中事件
};
}

<script lang="ts" setup name="operationCatalog">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCatalogManagementApi } from '/@/api/insurance/catalogManagement';
import type { InsuranceOperationCatalog } from '/@/api/insurance/catalogManagement';

const catalogApi = useCatalogManagementApi();

const state = reactive({
	tableLoading: false,
	syncLoading: false,
	showAdvanceQueryUI: false,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'lastSyncTime',
		order: 'descending',
	},
	tableData: [] as InsuranceOperationCatalog[],
});

// 页面加载时
onMounted(async () => {
	await handleQuery();
});

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	try {
		const result = await catalogApi.getLocalOperationCatalog(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
		state.tableParams.total = result?.total || 0;
		state.tableData = result?.items ?? [];
	} catch (error) {
		ElMessage.error('获取数据失败');
	} finally {
		state.tableLoading = false;
	}
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 同步目录
const handleSync = async () => {
	try {
		await ElMessageBox.confirm('此操作将同步手术目录，可能需要较长时间，是否继续？', '确认同步', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		state.syncLoading = true;
		const response = await catalogApi.syncOperationCatalog();
		if (response.data?.result) {
			ElMessage.success('同步完成');
			await handleQuery();
		}
	} catch (error) {
		ElMessage.error('同步失败');
	} finally {
		state.syncLoading = false;
	}
};

// 批量删除
const batchDelOperation = () => {
	ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			ElMessage.warning('批量删除功能暂未实现');
		})
		.catch(() => {});
};

// 格式化日期
const formatDate = (dateStr: string) => {
	if (!dateStr) return '-';
	return new Date(dateStr).toLocaleString('zh-CN');
};
</script>

<template>
	<div class="operation-catalog-container">
		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键词">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="手术编码、名称、拼音" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="注销标志">
							<el-select v-model="state.tableQueryParams.zxBz" placeholder="请选择" clearable>
								<el-option label="有效" value="0" />
								<el-option label="注销" value="1" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button type="danger" style="margin-left: 5px" icon="ele-Delete" @click="batchDelOperation" :disabled="state.selectData.length == 0"> 删除 </el-button>
								<el-button type="success" style="margin-left: 5px" icon="ele-Refresh" @click="handleSync" :loading="state.syncLoading"> 同步目录 </el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="ssBm" label="手术编码" width="120" fixed="left" show-overflow-tooltip />
				<el-table-column prop="ssMc" label="手术名称" width="300" show-overflow-tooltip />
				<el-table-column prop="py" label="拼音" width="150" show-overflow-tooltip />
				<el-table-column prop="zxBz" label="注销标志" width="100" align="center">
					<template #default="{ row }">
						<el-tag :type="row.zxBz === '0' ? 'success' : 'danger'" size="small">
							{{ row.zxBz === '0' ? '有效' : '注销' }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="lastSyncTime" label="最后同步时间" width="160" sortable="custom">
					<template #default="{ row }">
						<span>{{ formatDate(row.lastSyncTime) }}</span>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
		</el-card>
	</div>
</template>

<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>

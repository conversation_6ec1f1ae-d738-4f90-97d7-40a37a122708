# 技术栈

## 前端技术栈

- **框架**: Vue 3.x 使用 Composition API 和 `<script setup>` 语法
- **构建工具**: Vite 6.x 用于快速开发和优化构建
- **语言**: TypeScript 严格类型检查
- **UI 库**: Element Plus 2.x 综合组件库
- **状态管理**: Pinia 响应式状态管理
- **路由**: Vue Router 4.x 动态路由加载
- **样式**: SCSS 模块化架构
- **国际化**: Vue I18n 多语言支持

## 后端集成

- **API 通信**: Axios 自定义请求/响应拦截器
- **实时通信**: SignalR WebSocket 连接
- **身份验证**: 基于 JWT 的角色访问控制
- **API 生成**: 从 OpenAPI 规范自动生成 TypeScript API 客户端

## 开发工具

- **代码检查**: ESLint Vue 3 和 TypeScript 规则
- **代码格式化**: Prettier 一致的代码风格
- **包管理器**: pnpm（推荐）高效依赖管理
- **Node 版本**: 16+ 必需（最低 14.18+）

## 核心库

- **图表**: ECharts 数据可视化
- **表单**: VForm3 动态表单构建
- **编辑器**: WangEditor 富文本编辑
- **文件处理**: xlsx-js-style Excel 操作
- **工具库**: Lodash-es, VueUse 常用函数
- **动画**: Animate.css UI 过渡效果

## 构建和开发命令

```bash
# 安装依赖
pnpm install

# 开发服务器
pnpm run dev

# 生产构建
pnpm run build

# 代码检查和格式化
pnpm run lint-fix
pnpm run format

# API 构建（专业模块）
pnpm run build-api
pnpm run build-patient-api
pnpm run build-pharmacy-api
```

## 环境配置

- **开发环境**: 使用 `.env.development`，本地 API 地址 `http://localhost:5005`
- **生产环境**: 使用 `.env.production`，可配置 API 端点
- **默认凭据**: superadmin/123456（仅开发环境）

## 浏览器支持

- Chrome ≥ 87
- Firefox ≥ 78
- Safari ≥ 13
- Edge ≥ 88

注意：由于 Vue 3 要求，不支持 IE11。

/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { RegStatusEnum } from './reg-status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 
 *
 * @export
 * @interface RegisterOutput
 */
export interface RegisterOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof RegisterOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof RegisterOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof RegisterOutput
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    tenantId?: number | null;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    visitNo?: string | null;

    /**
     * 就诊次数
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    visitNum?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    timePeriodId?: number | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    regCategoryId?: number | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    patientId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    patientName?: string | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    wubiCode?: string | null;

    /**
     * @type {GenderEnum}
     * @memberof RegisterOutput
     */
    sex?: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    age?: number | null;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    ageUnit?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof RegisterOutput
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof RegisterOutput
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    idCardNum?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    occupation?: string | null;

    /**
     * 费别id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    feeId?: number | null;

    /**
     * 保险号码
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    insuranceNum?: string | null;

    /**
     * 保险类型
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    insuranceType?: string | null;

    /**
     * 合同单位id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    contractUnitId?: number | null;

    /**
     * 就诊类型 初诊|复诊
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    visitType?: number | null;

    /**
     * 就诊科室id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    deptId?: number | null;

    /**
     * 医生id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    doctorId?: number | null;

    /**
     * @type {RegStatusEnum}
     * @memberof RegisterOutput
     */
    status?: RegStatusEnum;

    /**
     * 症状
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    symptom?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    consultationFee?: number | null;

    /**
     * 其他费用
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    otherFee?: number | null;

    /**
     * 实收费用
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    actualChargeFee?: number | null;

    /**
     * 退号时间
     *
     * @type {Date}
     * @memberof RegisterOutput
     */
    refundNumTime?: Date | null;

    /**
     * 退号人id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    refundNumId?: number | null;

    /**
     * 就诊卡id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    cardId?: number | null;

    /**
     * 收费主表id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    chargeMainId?: number | null;

    /**
     * 预约流水号
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    appSerialNum?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    remark?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    nation?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    nativePlaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    residenceProvince?: number | null;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    residenceCity?: number | null;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    residenceCounty?: number | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    contactName?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    contactPhone?: string | null;

    /**
     * 医保卡余额
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    medInsCardBalance?: number | null;

    /**
     * 个人账户信息
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    personalAccountInfo?: string | null;

    /**
     * 医保个人编号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    medInsPersonalNum?: string | null;

    /**
     * 医保类型
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    medInsType?: number | null;

    /**
     * 医保统筹区号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    medInsAreaCode?: string | null;

    /**
     * 医保就诊编号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    medInsRegNum?: string | null;

    /**
     * 医保支付方式 0:门诊统筹 1：个人支付
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    medInsPayType?: string | null;

    /**
     * 结算病种id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    settleDiseaseTypeId?: number | null;

    /**
     * 首次就诊科室id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    firstDeptId?: number | null;

    /**
     * 首次就诊医生id
     *
     * @type {number}
     * @memberof RegisterOutput
     */
    firstDoctorId?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof RegisterOutput
     */
    isNoCard?: YesNoEnum;

    /**
     * 险种标志
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    insuranceSign?: string | null;

    /**
     * 门诊号 第一次就诊的流水号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    outpatientNo?: string | null;

    /**
     * 就医类别
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    medTreatCategory?: string | null;

    /**
     * 发病日期
     *
     * @type {Date}
     * @memberof RegisterOutput
     */
    onsetDate?: Date | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    diagnosticName?: string | null;

    /**
     * 病种代码
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    diseaseTypeCode?: string | null;

    /**
     * 病种名称
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    diseaseTypeName?: string | null;

    /**
     * 门诊急诊转诊标志
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    outEmeReferralSign?: string | null;

    /**
     * 外伤标志
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    traumaSign?: string | null;

    /**
     * 涉及第三方标志
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    thirdPartySign?: string | null;

    /**
     * 科室名称
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    deptName?: string | null;

    /**
     * 医生名称
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    doctorName?: string | null;

    /**
     * 费别
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    feeName?: string | null;

    /**
     * 号别
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    regCategory?: string | null;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof RegisterOutput
     */
    cardNo?: string | null;
}

# 项目结构

## 根目录

```
├── .env*                    # 环境配置文件
├── .eslintrc.js            # ESLint 配置
├── .prettierrc.cjs         # Prettier 格式化规则
├── package.json            # 依赖和脚本
├── tsconfig.json           # TypeScript 配置
├── vite.config.ts          # Vite 构建配置
├── index.html              # 主 HTML 模板
└── README.md               # 项目文档
```

## 源码目录 (`src/`)

### 核心应用文件

- `main.ts` - 应用入口点，插件注册
- `App.vue` - 根 Vue 组件，全局提供者

### API 层 (`src/api*/`)

- `api/` - 按领域组织的传统 API 模块（系统、医疗等）
- `api-services/` - 从 OpenAPI 自动生成的 TypeScript API 客户端
- `api-modules/` - 特定领域的模块化 API 实现
- `api-plugins/` - 插件特定的 API 集成（审批流、钉钉、GoView）

### 组件 (`src/components/`)

按功能组织的可复用 Vue 组件：

- `auth/` - 身份验证相关组件
- `table/` - 高级功能数据表格组件
- `editor/` - 富文本和代码编辑器
- `sysDict/` - 系统字典组件
- `pharmacy/`, `registration/` - 领域特定组件

### 应用架构

- `stores/` - Pinia 状态管理（用户、路由、主题等）
- `router/` - Vue Router 配置，动态加载
- `composables/` - 可复用组合函数
- `directive/` - 自定义 Vue 指令
- `utils/` - 工具函数和助手

### UI 和样式

- `theme/` - SCSS 样式表和主题
- `assets/` - 静态资源（图片、图标、字体）
- `layout/` - 应用布局组件（头部、侧边栏等）

### 视图 (`src/views/`)

按功能领域组织的页面组件：

- `system/` - 系统管理页面
- `patient/`, `pharmacy/`, `medicalTech/` - 医疗领域页面
- `financial/`, `insurance/` - 业务领域页面
- `login/`, `error/` - 身份验证和错误页面

### 配置和类型

- `types/` - TypeScript 类型定义
- `i18n/` - 国际化文件和翻译

## 后端结构 (`backend/`)

按标准层次组织的 .NET 后端：

- `Controllers/` - API 控制器
- `Services/` - 业务逻辑服务
- `Entities/` - 数据模型
- `Models/` - DTO 和视图模型
- `Configuration/` - 应用配置

## 构建和部署

- `api_build/` - 后端构建脚本和配置
- `scripts/` - 清理和维护的工具脚本
- `public/` - 直接提供的静态公共资源

## 命名约定

### 文件和目录

- 组件文件使用 kebab-case：`user-profile.vue`
- 代码中组件名使用 PascalCase：`UserProfile`
- TypeScript 文件使用 camelCase：`userService.ts`
- 工具目录使用小写：`utils/`, `stores/`

### Vue 组件

- 单文件组件使用 `<script setup>` 语法
- Props 和 emits 使用 TypeScript 接口类型
- 使用组合 API 和响应式引用

### API 组织

- 按业务领域分组 API（患者、药房、系统）
- CRUD 操作使用一致命名
- 自动生成的客户端遵循 OpenAPI 规范

## 导入别名

- `/@/` - 指向 `src/` 目录，用于清晰导入
- 示例：`import { useUserStore } from '/@/stores/userInfo'`

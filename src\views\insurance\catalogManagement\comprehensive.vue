<script lang="ts" setup name="comprehensive">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCatalogManagementApi } from '/@/api/insurance/catalogManagement';
import type { CatalogSyncStatusDto } from '/@/api/insurance/catalogManagement';

const catalogApi = useCatalogManagementApi();

const state = reactive({
	syncStatus: {
		hospitalItemCount: 0,
		insuranceItemCount: 0,
		sickCatalogCount: 0,
		operationCatalogCount: 0,
		dictionaryCount: 0,
	} as CatalogSyncStatusDto,
	syncLoading: false,
	syncLogs: [] as Array<{
		time: string;
		content: string;
		status: 'success' | 'error' | 'info';
		module: string;
	}>,
	systemHealth: {
		status: 'healthy',
		lastCheck: '',
		issues: [] as string[],
	},
});

// 页面加载时
onMounted(async () => {
	await loadSyncStatus();
	await loadSystemHealth();
	initializeLogs();
});

// 加载同步状态统计
const loadSyncStatus = async () => {
	try {
		const response = await catalogApi.getSyncStatus();
		if (response.data?.result) {
			state.syncStatus = response.data.result;
			addSyncLog('系统', '同步状态刷新成功', 'success', 'system');
		}
	} catch (error) {
		console.error('获取同步状态失败:', error);
		addSyncLog('系统', '同步状态刷新失败', 'error', 'system');
	}
};

// 加载系统健康状态
const loadSystemHealth = async () => {
	try {
		// 模拟系统健康检查
		state.systemHealth = {
			status: 'healthy',
			lastCheck: new Date().toLocaleString('zh-CN'),
			issues: [],
		};
		addSyncLog('系统', '系统健康检查完成', 'success', 'health');
	} catch (error) {
		state.systemHealth.status = 'error';
		addSyncLog('系统', '系统健康检查失败', 'error', 'health');
	}
};

// 初始化日志
const initializeLogs = () => {
	addSyncLog('系统', '综合管理模块启动', 'info', 'system');
	addSyncLog('监控', '开始监控目录同步状态', 'info', 'monitor');
};

// 一键同步所有目录
const handleSyncAll = async () => {
	try {
		const result = await ElMessageBox.confirm('此操作将同步所有目录数据，可能需要较长时间，是否继续？', '确认同步', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		if (result === 'confirm') {
			state.syncLoading = true;
			addSyncLog('同步', '开始一键同步所有目录', 'info', 'sync');

			const response = await catalogApi.syncAllCatalogs();
			if (response.data?.result) {
				addSyncLog('同步', `一键同步完成: ${response.data.result}`, 'success', 'sync');
				ElMessage.success('同步完成');
				await loadSyncStatus();
			}
		}
	} catch (error) {
		console.error('同步失败:', error);
		addSyncLog('同步', `一键同步失败: ${error}`, 'error', 'sync');
	} finally {
		state.syncLoading = false;
	}
};

// 清空日志
const clearLogs = () => {
	ElMessageBox.confirm('确定要清空所有日志吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			state.syncLogs = [];
			addSyncLog('系统', '日志已清空', 'info', 'system');
			ElMessage.success('日志已清空');
		})
		.catch(() => {});
};

// 导出日志
const exportLogs = () => {
	try {
		const logContent = state.syncLogs.map((log) => `${log.time} [${log.module}] ${log.status.toUpperCase()}: ${log.content}`).join('\n');

		const blob = new Blob([logContent], { type: 'text/plain' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = `sync-logs-${new Date().toISOString().slice(0, 10)}.txt`;
		a.click();
		URL.revokeObjectURL(url);

		addSyncLog('系统', '日志导出成功', 'success', 'export');
		ElMessage.success('日志导出成功');
	} catch (error) {
		addSyncLog('系统', '日志导出失败', 'error', 'export');
		ElMessage.error('日志导出失败');
	}
};

// 刷新统计数据
const refreshStats = async () => {
	await loadSyncStatus();
	await loadSystemHealth();
	ElMessage.success('统计数据已刷新');
};

// 添加同步日志
const addSyncLog = (module: string, content: string, status: 'success' | 'error' | 'info', type: string) => {
	state.syncLogs.unshift({
		time: new Date().toLocaleString('zh-CN'),
		content,
		status,
		module,
	});

	// 保持最多100条日志
	if (state.syncLogs.length > 100) {
		state.syncLogs = state.syncLogs.slice(0, 100);
	}
};

// 格式化日期
const formatDate = (dateStr: string) => {
	if (!dateStr) return '暂无';
	return new Date(dateStr).toLocaleString('zh-CN');
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
	switch (status) {
		case 'success':
			return 'success';
		case 'error':
			return 'danger';
		case 'info':
		default:
			return 'info';
	}
};

// 获取健康状态颜色
const getHealthColor = (status: string) => {
	switch (status) {
		case 'healthy':
			return '#67c23a';
		case 'warning':
			return '#e6a23c';
		case 'error':
			return '#f56c6c';
		default:
			return '#909399';
	}
};
</script>

<template>
	<div class="comprehensive-container">
		<!-- 系统状态面板 -->
		<el-card shadow="hover" class="mb15">
			<template #header>
				<div class="card-header">
					<span>系统状态监控</span>
					<el-button type="primary" size="small" @click="refreshStats">
						<el-icon><ele-Refresh /></el-icon>
						刷新状态
					</el-button>
				</div>
			</template>
			<el-row :gutter="20">
				<el-col :span="6">
					<div class="status-item">
						<div class="status-icon" :style="{ backgroundColor: getHealthColor(state.systemHealth.status) }">
							<el-icon><ele-Monitor /></el-icon>
						</div>
						<div class="status-info">
							<h4>系统健康</h4>
							<p>{{ state.systemHealth.status === 'healthy' ? '正常' : '异常' }}</p>
							<small>最后检查：{{ state.systemHealth.lastCheck }}</small>
						</div>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="status-item">
						<div class="status-icon hospital">
							<el-icon><ele-Hospital /></el-icon>
						</div>
						<div class="status-info">
							<h4>医院项目</h4>
							<p>{{ state.syncStatus.hospitalItemCount || 0 }} 条</p>
							<small v-if="state.syncStatus.lastHospitalSync"> 最后同步：{{ formatDate(state.syncStatus.lastHospitalSync) }} </small>
						</div>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="status-item">
						<div class="status-icon insurance">
							<el-icon><ele-Document /></el-icon>
						</div>
						<div class="status-info">
							<h4>医保项目</h4>
							<p>{{ state.syncStatus.insuranceItemCount || 0 }} 条</p>
							<small v-if="state.syncStatus.lastInsuranceSync"> 最后同步：{{ formatDate(state.syncStatus.lastInsuranceSync) }} </small>
						</div>
					</div>
				</el-col>
				<el-col :span="6">
					<div class="status-item">
						<div class="status-icon dictionary">
							<el-icon><ele-Setting /></el-icon>
						</div>
						<div class="status-info">
							<h4>数据字典</h4>
							<p>{{ state.syncStatus.dictionaryCount || 0 }} 条</p>
							<small v-if="state.syncStatus.lastDictionarySync"> 最后同步：{{ formatDate(state.syncStatus.lastDictionarySync) }} </small>
						</div>
					</div>
				</el-col>
			</el-row>
		</el-card>

		<!-- 批量操作面板 -->
		<el-card shadow="hover" class="mb15">
			<template #header>
				<span>批量操作</span>
			</template>
			<el-row :gutter="20">
				<el-col :span="8">
					<el-button type="primary" size="large" :loading="state.syncLoading" @click="handleSyncAll" style="width: 100%; height: 50px">
						<el-icon><ele-Refresh /></el-icon>
						一键同步所有目录
					</el-button>
				</el-col>
				<el-col :span="8">
					<el-button type="success" size="large" @click="refreshStats" style="width: 100%; height: 50px">
						<el-icon><ele-DataAnalysis /></el-icon>
						刷新统计数据
					</el-button>
				</el-col>
				<el-col :span="8">
					<el-button type="warning" size="large" @click="exportLogs" style="width: 100%; height: 50px">
						<el-icon><ele-Download /></el-icon>
						导出同步日志
					</el-button>
				</el-col>
			</el-row>
		</el-card>

		<!-- 同步日志 -->
		<el-card shadow="hover">
			<template #header>
				<div class="card-header">
					<span>同步日志 ({{ state.syncLogs.length }})</span>
					<el-button type="danger" size="small" @click="clearLogs">
						<el-icon><ele-Delete /></el-icon>
						清空日志
					</el-button>
				</div>
			</template>
			<div class="sync-log-container">
				<div class="log-item" v-for="(log, index) in state.syncLogs" :key="index">
					<div class="log-time">{{ log.time }}</div>
					<div class="log-module">
						<el-tag size="small" type="info">{{ log.module }}</el-tag>
					</div>
					<div class="log-content">{{ log.content }}</div>
					<div class="log-status">
						<el-tag :type="getStatusTagType(log.status)" size="small">
							{{ log.status === 'success' ? '成功' : log.status === 'error' ? '失败' : '信息' }}
						</el-tag>
					</div>
				</div>
				<div v-if="state.syncLogs.length === 0" class="empty-logs">
					<el-empty description="暂无日志记录" />
				</div>
			</div>
		</el-card>
	</div>
</template>

<style lang="scss" scoped>
.comprehensive-container {
	padding: 15px;

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.status-item {
		display: flex;
		align-items: center;
		padding: 20px;
		border: 1px solid #ebeef5;
		border-radius: 6px;
		transition: all 0.3s ease;

		&:hover {
			box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
		}

		.status-icon {
			width: 50px;
			height: 50px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 15px;

			.el-icon {
				font-size: 20px;
				color: white;
			}

			&.hospital {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			}

			&.insurance {
				background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
			}

			&.dictionary {
				background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
			}
		}

		.status-info {
			flex: 1;

			h4 {
				margin: 0 0 5px 0;
				font-size: 14px;
				font-weight: 600;
				color: #303133;
			}

			p {
				margin: 0 0 5px 0;
				font-size: 18px;
				font-weight: 600;
				color: #409eff;
			}

			small {
				font-size: 12px;
				color: #909399;
			}
		}
	}

	.sync-log-container {
		max-height: 500px;
		overflow-y: auto;

		.log-item {
			display: flex;
			align-items: center;
			padding: 12px 0;
			border-bottom: 1px solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}

			.log-time {
				width: 150px;
				font-size: 12px;
				color: #909399;
			}

			.log-module {
				width: 80px;
				margin-right: 10px;
			}

			.log-content {
				flex: 1;
				font-size: 14px;
				color: #606266;
				margin-right: 10px;
			}

			.log-status {
				width: 60px;
				text-align: right;
			}
		}

		.empty-logs {
			padding: 40px 0;
			text-align: center;
		}
	}
}
</style>

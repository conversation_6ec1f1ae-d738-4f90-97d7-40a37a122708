import { useBaseApi } from '/@/api/base';

// 医保目录管理接口服务
export const useCatalogManagementApi = () => {
	const baseApi = useBaseApi('Insurance/CatalogManagement');
	return {
		// 获取医院项目自付比例
		getItemSelfPayRatio: (data: any) => baseApi.post('GetItemSelfPayRatio', data),
		// 同步医院项目目录
		syncHospitalItems: (data: any) => baseApi.post('SyncHospitalItems', data),
		// 增量同步医院项目目录
		incrementalSyncHospitalItems: () => baseApi.post('IncrementalSyncHospitalItems', {}),
		// 完整增量同步医院项目目录
		fullIncrementalSyncHospitalItems: () => baseApi.post('FullIncrementalSyncHospitalItems', {}),
		// 新增医院项目
		addHospitalItem: (data: any) => baseApi.post('AddHospitalItem', data),
		// 获取本地医院项目目录
		getLocalHospitalItems: (params: any) => baseApi.get('GetLocalHospitalItems', params),
		// 根据编码获取医院项目
		getHospitalItemByCode: (yyxmBm: string) => baseApi.get('GetHospitalItemByCode', { yyxmBm }),
		// 同步医保项目目录
		syncInsuranceItems: () => baseApi.post('SyncInsuranceItems', {}),
		// 增量同步医保项目目录
		incrementalSyncInsuranceItems: () => baseApi.post('IncrementalSyncInsuranceItems', {}),
		// 获取本地医保项目目录
		getLocalInsuranceItems: (params: any) => baseApi.get('GetLocalInsuranceItems', params),
		// 同步疾病目录
		syncSickCatalog: () => baseApi.post('SyncSickCatalog', {}),
		// 增量同步疾病目录
		incrementalSyncSickCatalog: () => baseApi.post('IncrementalSyncSickCatalog', {}),
		// 获取本地疾病目录
		getLocalSickCatalog: (params: any) => baseApi.get('GetLocalSickCatalog', params),
		// 同步手术目录
		syncOperationCatalog: () => baseApi.post('SyncOperationCatalog', {}),
		// 获取本地手术目录
		getLocalOperationCatalog: (params: any) => baseApi.get('GetLocalOperationCatalog', params),
		// 同步数据字典
		syncDictionary: (dmbh: string) => baseApi.post('SyncDictionary', { dmbh }),
		// 批量同步常用数据字典
		syncCommonDictionaries: () => baseApi.post('SyncCommonDictionaries', {}),
		// 获取数据字典
		getDictionary: (dmbh: string) => baseApi.get('GetDictionary', { dmbh }),
		// 获取所有数据字典分类
		getDictionaryCategories: () => baseApi.get('GetDictionaryCategories', {}),
		// 分页获取数据字典
		getDictionaryPaged: (params: any) => baseApi.get('GetDictionaryPaged', params),
		// 同步所有目录数据
		syncAllCatalogs: () => baseApi.post('SyncAllCatalogs', {}),
		// 获取同步状态统计
		getSyncStatus: () => baseApi.get('GetSyncStatus', {}),
		// 新增或更新医师信息
		addDoctorInfo: (data: any) => baseApi.post('AddDoctorInfo', data),
		// 查询医院医师信息
		queryDoctorInfo: (data: any) => baseApi.post('QueryDoctorInfo', data),
		// 获取本地医师信息
		getLocalDoctorInfo: (params: any) => baseApi.get('GetLocalDoctorInfo', params),
		// 获取本地限价信息
		getLocalLimitPriceInfo: (params: any) => baseApi.get('GetLocalLimitPriceInfo', params),
		// 获取本地首先自付比例信息
		getLocalFirstSelfPayRatioInfo: (params: any) => baseApi.get('GetLocalFirstSelfPayRatioInfo', params),
	};
};

// 医院项目目录实体
export interface HospitalItemCatalog {
	id: number;
	// 医院项目编码
	yyxmbm: string;
	// 医院项目名称
	yyxmmc: string;
	// 门诊结算项目编号
	mzjsxmbh?: string;
	// 住院结算项目编号
	zyjsxmbh?: string;
	// 自付比例
	zfbl?: number;
	// 自付比例说明
	sm?: string;
	// 医疗项目编码
	ylxmbm?: string;
	// 规格
	gg?: string;
	// 单位
	dw?: string;
	// 参考价
	ckj?: number;
	// 剂型
	jxm?: string;
	// 生产企业
	scqy?: string;
	// 药品标志
	ypbz?: string;
	// 险种标志
	xzbz?: string;
	// 人群类别
	rqlb?: string;
	// 单价
	dj: number;
	// 同步序号
	sxh?: number;
	// 最后同步时间
	lastSyncTime?: string;
	// 起始日期
	qsrq?: string;
	// 终止日期
	zzrq?: string;
	// 审批标志
	spbz?: string;
	// 更新时间
	gxsj?: string;
}

// 医保项目目录实体
export interface InsuranceItemCatalog {
	id: number;
	// 医疗项目编码
	ylxmBm: string;
	// 医疗项目标准名称
	ylxmBzmc: string;
	// 拼音
	py?: string;
	// 药品标志
	ypBz?: string;
	// 注销标志
	zxBz?: string;
	// 最后同步时间
	lastSyncTime?: string;
}

// 疾病目录实体
export interface InsuranceSickCatalog {
	id: number;
	// 疾病编码
	jbBm: string;
	// 疾病名称
	jbMc: string;
	// 拼音
	py?: string;
	// 门诊大病类别
	mzdbLb?: string;
	// 注销标志
	zxBz?: string;
	// 最后同步时间
	lastSyncTime?: string;
}

// 手术目录实体
export interface InsuranceOperationCatalog {
	id: number;
	// 手术编码
	ssBm: string;
	// 手术名称
	ssMc: string;
	// 拼音
	py?: string;
	// 注销标志
	zxBz?: string;
	// 最后同步时间
	lastSyncTime?: string;
}

// 数据字典实体
export interface InsuranceDictionary {
	id: number;
	// 代码编号
	dmBh: string;
	// 代码值
	code: string;
	// 字典名称
	content: string;
	// 最后同步时间
	lastSyncTime?: string;
}

// 医师信息实体
export interface DoctorInfo {
	id: number;
	// 医师编码
	ysBm: string;
	// 医师姓名
	ysXm: string;
	// 科室编码
	ksBm?: string;
	// 最后同步时间
	lastSyncTime?: string;
}

// 目录同步状态DTO
export interface CatalogSyncStatusDto {
	// 医院项目数量
	hospitalItemCount: number;
	// 医保项目数量
	insuranceItemCount: number;
	// 疾病目录数量
	sickCatalogCount: number;
	// 手术目录数量
	operationCatalogCount: number;
	// 数据字典数量
	dictionaryCount: number;
	// 医院项目最后同步时间
	lastHospitalSync?: string;
	// 医保项目最后同步时间
	lastInsuranceSync?: string;
	// 疾病目录最后同步时间
	lastSickSync?: string;
	// 手术目录最后同步时间
	lastOperationSync?: string;
	// 数据字典最后同步时间
	lastDictionarySync?: string;
}

// 限价信息实体
export interface LimitPriceInfo {
	id?: string;
	ylxmBm?: string; // 医疗项目编码
	ylxmMc?: string; // 医疗项目名称
	xjje?: number; // 限价金额
	rqLb?: string; // 人群类别
	qsrq?: string; // 起始日期
	zzrq?: string; // 终止日期
	lastSyncTime?: string; // 最后同步时间
}

// 首先自付比例信息实体
export interface FirstSelfPayRatioInfo {
	id?: string;
	ylxmBm?: string; // 医疗项目编码
	ylxmMc?: string; // 医疗项目名称
	zfbl?: number; // 自付比例
	xzBz?: string; // 险种标志
	qsrq?: string; // 起始日期
	zzrq?: string; // 终止日期
	lastSyncTime?: string; // 最后同步时间
}

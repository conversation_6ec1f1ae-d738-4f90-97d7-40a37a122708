/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { RegStatusEnum } from './reg-status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 挂号输入参数
 *
 * @export
 * @interface AddMzRegisterInput
 */
export interface AddMzRegisterInput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof AddMzRegisterInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof AddMzRegisterInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof AddMzRegisterInput
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    tenantId?: number | null;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    visitNo?: string | null;

    /**
     * 就诊次数
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    visitNum?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    timePeriodId?: number | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    patientId?: number | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    wubiCode?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof AddMzRegisterInput
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof AddMzRegisterInput
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    idCardNum?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    occupation?: string | null;

    /**
     * 保险号码
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    insuranceNum?: string | null;

    /**
     * 保险类型
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    insuranceType?: string | null;

    /**
     * 合同单位id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    contractUnitId?: number | null;

    /**
     * 就诊类型 初诊|复诊
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    visitType?: number | null;

    /**
     * @type {RegStatusEnum}
     * @memberof AddMzRegisterInput
     */
    status?: RegStatusEnum;

    /**
     * 症状
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    symptom?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    consultationFee?: number | null;

    /**
     * 其他费用
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    otherFee?: number | null;

    /**
     * 实收费用
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    actualChargeFee?: number | null;

    /**
     * 退号时间
     *
     * @type {Date}
     * @memberof AddMzRegisterInput
     */
    refundNumTime?: Date | null;

    /**
     * 退号人id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    refundNumId?: number | null;

    /**
     * 就诊卡id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    cardId?: number | null;

    /**
     * 收费主表id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    chargeMainId?: number | null;

    /**
     * 预约流水号
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    appSerialNum?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    remark?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    nation?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    nativePlaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    residenceProvince?: number | null;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    residenceCity?: number | null;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    residenceCounty?: number | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    contactName?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    contactPhone?: string | null;

    /**
     * 医保卡余额
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    medInsCardBalance?: number | null;

    /**
     * 个人账户信息
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    personalAccountInfo?: string | null;

    /**
     * 医保个人编号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    medInsPersonalNum?: string | null;

    /**
     * 医保类型
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    medInsType?: number | null;

    /**
     * 医保统筹区号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    medInsAreaCode?: string | null;

    /**
     * 医保就诊编号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    medInsRegNum?: string | null;

    /**
     * 医保支付方式 0:门诊统筹 1：个人支付
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    medInsPayType?: string | null;

    /**
     * 结算病种id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    settleDiseaseTypeId?: number | null;

    /**
     * 首次就诊科室id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    firstDeptId?: number | null;

    /**
     * 首次就诊医生id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    firstDoctorId?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof AddMzRegisterInput
     */
    isNoCard?: YesNoEnum;

    /**
     * 险种标志
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    insuranceSign?: string | null;

    /**
     * 门诊号 第一次就诊的流水号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    outpatientNo?: string | null;

    /**
     * 就医类别
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    medTreatCategory?: string | null;

    /**
     * 发病日期
     *
     * @type {Date}
     * @memberof AddMzRegisterInput
     */
    onsetDate?: Date | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    diagnosticName?: string | null;

    /**
     * 病种代码
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    diseaseTypeCode?: string | null;

    /**
     * 病种名称
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    diseaseTypeName?: string | null;

    /**
     * 门诊急诊转诊标志
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    outEmeReferralSign?: string | null;

    /**
     * 外伤标志
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    traumaSign?: string | null;

    /**
     * 涉及第三方标志
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    thirdPartySign?: string | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    regCategoryId: number;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    patientName: string;

    /**
     * @type {GenderEnum}
     * @memberof AddMzRegisterInput
     */
    sex: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    age: number;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    ageUnit: string;

    /**
     * 费别
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    feeId: number;

    /**
     * 科室
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    deptId: number;

    /**
     * 医生
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    doctorId: number;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof AddMzRegisterInput
     */
    cardNo?: string | null;

    /**
     * 总金额
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    totalAmount?: number | null;

    /**
     * 支付方式
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    paymentMethod?: number | null;

    /**
     * 个人支付
     *
     * @type {number}
     * @memberof AddMzRegisterInput
     */
    personalPayment?: number | null;
}

<script lang="ts" setup name="catalogManagement">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCatalogManagementApi } from '/@/api/insurance/catalogManagement';
import type { CatalogSyncStatusDto } from '/@/api/insurance/catalogManagement';

const router = useRouter();
const catalogApi = useCatalogManagementApi();

const state = reactive({
	syncStatus: {
		hospitalItemCount: 0,
		insuranceItemCount: 0,
		sickCatalogCount: 0,
		operationCatalogCount: 0,
		dictionaryCount: 0,
	} as CatalogSyncStatusDto,
	syncLoading: false,
	syncLogs: [] as Array<{ time: string; content: string; status: string }>,
});

// 功能模块配置
const modules = reactive([
	{
		key: 'hospitalItems',
		title: '医院项目目录',
		description: '管理医院内部项目目录，支持同步和新增',
		route: '/insurance/catalog/hospitalItems',
		icon: 'hospital',
	},
	{
		key: 'insuranceItems',
		title: '医保项目目录',
		description: '管理医保核心端项目目录数据',
		route: '/insurance/catalog/insuranceItems',
		icon: 'insurance',
	},
	{
		key: 'sickCatalog',
		title: '疾病目录',
		description: '管理疾病诊断相关目录信息',
		route: '/insurance/catalog/sickCatalog',
		icon: 'sick',
	},
	{
		key: 'operationCatalog',
		title: '手术目录',
		description: '管理手术操作相关目录信息',
		route: '/insurance/catalog/operationCatalog',
		icon: 'operation',
	},
	{
		key: 'dictionary',
		title: '数据字典',
		description: '管理系统数据字典和编码表',
		route: '/insurance/catalog/dictionary',
		icon: 'dictionary',
	},
	{
		key: 'doctorInfo',
		title: '医师信息',
		description: '管理医师基本信息和资质',
		route: '/insurance/catalog/doctorInfo',
		icon: 'doctor',
	},
	{
		key: 'comprehensive',
		title: '综合管理',
		description: '系统监控、批量操作和同步日志',
		route: '/insurance/catalog/comprehensive',
		icon: 'comprehensive',
	},
]);

// 页面加载时获取统计数据
onMounted(async () => {
	await loadSyncStatus();
	addSyncLog('系统启动', '医保目录管理系统启动完成', 'success');
});

// 加载同步状态统计
const loadSyncStatus = async () => {
	try {
		const response = await catalogApi.getSyncStatus();
		if (response.data?.result) {
			state.syncStatus = response.data.result;
			addSyncLog('系统', '同步状态刷新成功', 'success');
		}
	} catch (error) {
		console.error('获取同步状态失败:', error);
		addSyncLog('系统', '同步状态刷新失败', 'error');
	}
};

// 格式化日期
const formatDate = (dateStr: string) => {
	if (!dateStr) return '暂无';
	return new Date(dateStr).toLocaleString('zh-CN');
};

// 获取模块记录数量
const getModuleCount = (moduleKey: string) => {
	switch (moduleKey) {
		case 'hospitalItems':
			return state.syncStatus.hospitalItemCount;
		case 'insuranceItems':
			return state.syncStatus.insuranceItemCount;
		case 'sickCatalog':
			return state.syncStatus.sickCatalogCount;
		case 'operationCatalog':
			return state.syncStatus.operationCatalogCount;
		case 'dictionary':
			return state.syncStatus.dictionaryCount;
		default:
			return 0;
	}
};

// 导航到模块页面
const navigateToModule = (moduleKey: string) => {
	const module = modules.find((m) => m.key === moduleKey);
	if (module?.route) {
		router.push(module.route);
	}
};

// 一键同步所有目录
const handleSyncAll = async () => {
	try {
		const result = await ElMessageBox.confirm('此操作将同步所有目录数据，可能需要较长时间，是否继续？', '确认同步', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		if (result === 'confirm') {
			state.syncLoading = true;
			addSyncLog('同步', '开始一键同步所有目录', 'success');

			const response = await catalogApi.syncAllCatalogs();
			if (response.data?.result) {
				addSyncLog('同步', `一键同步完成: ${response.data.result}`, 'success');
				ElMessage.success('同步完成');
				await loadSyncStatus();
			}
		}
	} catch (error) {
		console.error('同步失败:', error);
		addSyncLog('同步', `一键同步失败: ${error}`, 'error');
	} finally {
		state.syncLoading = false;
	}
};

// 刷新统计数据
const refreshStats = async () => {
	await loadSyncStatus();
	ElMessage.success('统计数据已刷新');
};

// 查看日志
const handleViewLogs = () => {
	router.push('/insurance/catalog/comprehensive');
};

// 添加同步日志
const addSyncLog = (type: string, content: string, status: 'success' | 'error') => {
	state.syncLogs.unshift({
		time: new Date().toLocaleString('zh-CN'),
		content,
		status,
	});

	// 保持最多50条日志
	if (state.syncLogs.length > 50) {
		state.syncLogs = state.syncLogs.slice(0, 50);
	}
};
</script>

<template>
	<div class="catalog-management-container">
		<div class="catalog-management">
			<!-- 快捷操作 -->
			<el-card shadow="hover" class="mb15">
				<template #header>
					<span>快捷操作</span>
				</template>
				<el-row :gutter="10">
					<el-col :span="24">
						<el-button-group>
							<el-button plain type="primary" icon="ele-Refresh" :loading="state.syncLoading" @click="handleSyncAll"> 一键同步所有目录 </el-button>
							<el-button plain type="primary" icon="ele-DataAnalysis" @click="refreshStats"> 刷新统计数据 </el-button>
							<el-button plain type="primary" icon="ele-View" @click="handleViewLogs"> 查看同步日志 </el-button>
						</el-button-group>
					</el-col>
				</el-row>
			</el-card>

			<!-- 统计面板 -->
			<el-card shadow="hover" class="mb15">
				<template #header>
					<div class="card-header">
						<span>目录数据统计</span>
						<el-button type="primary" size="small" @click="refreshStats">
							<el-icon><ele-Refresh /></el-icon>
							刷新统计
						</el-button>
					</div>
				</template>
				<el-row :gutter="20">
					<el-col :span="6">
						<div class="stats-item">
							<div class="stats-content">
								<div class="stats-icon hospital">
									<el-icon><ele-Document /></el-icon>
								</div>
								<div class="stats-info">
									<h3>{{ state.syncStatus.hospitalItemCount || 0 }}</h3>
									<p>医院项目</p>
									<small v-if="state.syncStatus.lastHospitalSync"> 最后同步：{{ formatDate(state.syncStatus.lastHospitalSync) }} </small>
								</div>
							</div>
						</div>
					</el-col>
					<el-col :span="6">
						<div class="stats-item">
							<div class="stats-content">
								<div class="stats-icon insurance">
									<el-icon><ele-Document /></el-icon>
								</div>
								<div class="stats-info">
									<h3>{{ state.syncStatus.insuranceItemCount || 0 }}</h3>
									<p>医保项目</p>
									<small v-if="state.syncStatus.lastInsuranceSync"> 最后同步：{{ formatDate(state.syncStatus.lastInsuranceSync) }} </small>
								</div>
							</div>
						</div>
					</el-col>
					<el-col :span="6">
						<div class="stats-item">
							<div class="stats-content">
								<div class="stats-icon sick">
									<el-icon><ele-FirstAidKit /></el-icon>
								</div>
								<div class="stats-info">
									<h3>{{ state.syncStatus.sickCatalogCount || 0 }}</h3>
									<p>疾病目录</p>
									<small v-if="state.syncStatus.lastSickSync"> 最后同步：{{ formatDate(state.syncStatus.lastSickSync) }} </small>
								</div>
							</div>
						</div>
					</el-col>
					<el-col :span="6">
						<div class="stats-item">
							<div class="stats-content">
								<div class="stats-icon operation">
									<el-icon><ele-Operation /></el-icon>
								</div>
								<div class="stats-info">
									<h3>{{ state.syncStatus.operationCatalogCount || 0 }}</h3>
									<p>手术目录</p>
									<small v-if="state.syncStatus.lastOperationSync"> 最后同步：{{ formatDate(state.syncStatus.lastOperationSync) }} </small>
								</div>
							</div>
						</div>
					</el-col>
				</el-row>
			</el-card>

			<!-- 功能模块导航 -->
			<el-card shadow="hover">
				<template #header>
					<span>功能模块</span>
				</template>
				<el-row :gutter="20" class="module-nav">
					<el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="6" v-for="module in modules" :key="module.key">
						<div class="module-item" @click="navigateToModule(module.key)">
							<div class="module-content">
								<div class="module-icon" :class="module.icon">
									<el-icon v-if="module.icon === 'hospital'"><ele-Document /></el-icon>
									<el-icon v-else-if="module.icon === 'insurance'"><ele-Document /></el-icon>
									<el-icon v-else-if="module.icon === 'sick'"><ele-FirstAidKit /></el-icon>
									<el-icon v-else-if="module.icon === 'operation'"><ele-Operation /></el-icon>
									<el-icon v-else-if="module.icon === 'dictionary'"><ele-Setting /></el-icon>
									<el-icon v-else-if="module.icon === 'doctor'"><ele-User /></el-icon>
									<el-icon v-else><ele-DataAnalysis /></el-icon>
								</div>
								<h4>{{ module.title }}</h4>
								<p>{{ module.description }}</p>
							</div>
							<div class="module-count">{{ getModuleCount(module.key) }} 条记录</div>
						</div>
					</el-col>
				</el-row>
			</el-card>

			<!-- 同步日志 -->
			<el-card shadow="hover" class="mt15" v-if="state.syncLogs.length > 0">
				<template #header>
					<span>最近同步日志</span>
				</template>
				<div class="sync-log">
					<div class="log-item" v-for="(log, index) in state.syncLogs.slice(0, 5)" :key="index">
						<span class="log-time">{{ log.time }}</span>
						<span class="log-content">{{ log.content }}</span>
						<el-tag :type="log.status === 'success' ? 'success' : 'danger'" size="small">
							{{ log.status === 'success' ? '成功' : '失败' }}
						</el-tag>
					</div>
				</div>
			</el-card>
		</div>
	</div>
</template>

<style lang="scss" scoped>
.catalog-management-container {
	height: 100vh;
	overflow-y: auto;
	padding: 0;

	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;

		&:hover {
			background: #a8a8a8;
		}
	}
}

.catalog-management {
	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	:deep(.el-card__header) {
		padding: 5px 20px;
	}
	.stats-item {
		padding: 15px;
		border: 1px solid #ebeef5;
		border-radius: 8px;
		transition: all 0.3s ease;
		background: #fff;
		height: 90px;
		display: flex;
		align-items: center;

		&:hover {
			box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
			transform: translateY(-2px);
		}

		.stats-content {
			display: flex;
			align-items: center;

			.stats-icon {
				width: 45px;
				height: 45px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 12px;

				.el-icon {
					font-size: 18px;
					color: white;
				}

				&.hospital {
					background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				}

				&.insurance {
					background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
				}

				&.sick {
					background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
				}

				&.operation {
					background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
				}
			}

			.stats-info {
				flex: 1;

				h3 {
					margin: 0 0 3px 0;
					font-size: 22px;
					font-weight: 600;
					color: #303133;
				}

				p {
					margin: 0 0 3px 0;
					font-size: 13px;
					color: #606266;
				}

				small {
					font-size: 11px;
					color: #909399;
				}
			}
		}
	}

	.module-item {
		padding: 15px;
		border: 1px solid #ebeef5;
		border-radius: 8px;
		cursor: pointer;
		transition: all 0.3s ease;
		text-align: center;
		margin-bottom: 15px;
		height: 160px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		background: #fff;
		overflow: hidden;

		&:hover {
			border-color: #409eff;
			box-shadow: 0 4px 20px 0 rgba(64, 158, 255, 0.15);
			transform: translateY(-2px);
		}

		.module-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.module-icon {
			width: 45px;
			height: 45px;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 0 auto 12px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

			.el-icon {
				font-size: 20px;
				color: white;
			}

			&.hospital {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			}

			&.insurance {
				background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
			}

			&.sick {
				background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
			}

			&.operation {
				background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
			}

			&.dictionary {
				background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
			}

			&.doctor {
				background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
			}

			&.comprehensive {
				background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
			}
		}

		h4 {
			margin: 0 0 8px 0;
			font-size: 15px;
			font-weight: 600;
			color: #303133;
			line-height: 1.2;
		}

		p {
			margin: 0;
			font-size: 12px;
			color: #909399;
			line-height: 1.4;
			text-align: center;
			padding: 0 8px;
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			-webkit-box-orient: vertical;
		}

		.module-count {
			margin-top: 8px;
			padding: 5px 12px;
			background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
			color: white;
			border-radius: 15px;
			font-size: 12px;
			font-weight: 500;
			box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
		}
	}

	.sync-log {
		.log-item {
			display: flex;
			align-items: center;
			padding: 8px 0;
			border-bottom: 1px solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}

			.log-time {
				width: 150px;
				font-size: 12px;
				color: #909399;
			}

			.log-content {
				flex: 1;
				font-size: 14px;
				color: #606266;
				margin: 0 10px;
			}
		}
	}

	// 响应式优化
	@media (max-width: 768px) {
		.stats-item {
			margin-bottom: 12px;
			height: 75px;
			padding: 12px;

			.stats-content {
				.stats-icon {
					width: 40px;
					height: 40px;
					margin-right: 10px;

					.el-icon {
						font-size: 16px;
					}
				}

				.stats-info {
					h3 {
						font-size: 18px;
					}

					p {
						font-size: 12px;
					}

					small {
						font-size: 10px;
					}
				}
			}
		}

		.module-item {
			height: 140px;
			padding: 12px;
			margin-bottom: 12px;

			.module-icon {
				width: 40px;
				height: 40px;
				margin-bottom: 10px;

				.el-icon {
					font-size: 18px;
				}
			}

			h4 {
				font-size: 14px;
				margin-bottom: 6px;
			}

			p {
				font-size: 11px;
			}

			.module-count {
				font-size: 11px;
				padding: 4px 10px;
			}
		}
	}

	@media (max-width: 480px) {
		.catalog-management-container {
			height: calc(100vh - 60px);
		}

		.module-item {
			height: 120px;
			padding: 10px;

			.module-icon {
				width: 35px;
				height: 35px;
				margin-bottom: 8px;

				.el-icon {
					font-size: 16px;
				}
			}

			h4 {
				font-size: 13px;
				margin-bottom: 4px;
			}

			p {
				font-size: 10px;
				-webkit-line-clamp: 1;
				line-clamp: 1;
			}

			.module-count {
				font-size: 10px;
				padding: 3px 8px;
				margin-top: 4px;
			}
		}
	}
}
</style>

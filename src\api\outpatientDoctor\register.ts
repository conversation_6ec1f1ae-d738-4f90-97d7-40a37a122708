import { useBaseApi } from '/@/api/base';

// 门诊挂号接口服务
export const useRegisterApi = () => {
	const baseApi = useBaseApi('register');
	return {
		// 分页查询就诊记录
		page: baseApi.page,
		// 挂号
		add: baseApi.add,
		// 退号
		refund: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'refund',
					method: 'post',
					data,
				},
				cancel
			);
		},
		delete: baseApi.delete,
		// 更新挂号信息
		update: baseApi.update,
		// 获取门诊挂号详情
		detail: baseApi.detail,
		// 根据挂号状态查询挂号列表
		listByStatus: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'listByStatus',
					method: 'get',
					data,
				},
				cancel
			);
		},
		// 获取未分诊挂号列表
		getUnTriageRegister: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getUnTriageRegister',
					method: 'post',
					data,
				},
				cancel
			);
		},
		// 根据门诊号获取挂号列表
		getRegisterByOutpatientNo: function (data: any, cancel: boolean = false) {
			return baseApi.request(
				{
					url: baseApi.baseUrl + 'getRegisterByOutpatientNo',
					method: 'get',
					data,
				},
				cancel
			);
		},
	};
};

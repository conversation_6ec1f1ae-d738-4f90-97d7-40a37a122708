# 产品概述

Admin.NET 是一个基于 .NET 的综合性管理框架，配备 Vue 3 前端。它被设计为通用权限管理系统，用于企业应用的快速开发。

## 核心特性

- **多平台支持**: 响应式设计，支持手机、平板和桌面端
- **权限管理**: 内置基于角色的访问控制系统
- **医疗系统专注**: 专业的医疗模块，包括患者管理、药房、医技和门诊服务
- **多租户架构**: 支持租户隔离和管理
- **国际化**: 内置多语言 i18n 支持
- **实时通信**: SignalR 集成实现实时更新

## 目标用户

- 需要综合管理系统的医疗机构
- 需要强大权限管理的企业应用
- 需要多租户 SaaS 解决方案的组织
- 寻求快速开发框架的开发团队

## 核心模块

- 系统管理和配置
- 用户和角色管理
- 医疗工作流管理（患者、药房、门诊、住院）
- 财务管理
- 审批工作流
- 实时通知和消息

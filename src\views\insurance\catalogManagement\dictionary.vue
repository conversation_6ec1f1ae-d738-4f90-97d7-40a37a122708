<script lang="ts" setup name="dictionary">
import { ref, reactive, onMounted } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useCatalogManagementApi } from '/@/api/insurance/catalogManagement';
import type { InsuranceDictionary } from '/@/api/insurance/catalogManagement';

const catalogApi = useCatalogManagementApi();

const state = reactive({
	tableLoading: false,
	syncCommonLoading: false,
	syncSingleLoading: false,
	showAdvanceQueryUI: false,
	selectData: [] as any[],
	tableQueryParams: {} as any,
	tableParams: {
		page: 1,
		pageSize: 20,
		total: 0,
		field: 'lastSyncTime',
		order: 'descending',
	},
	tableData: [] as InsuranceDictionary[],
	categories: [] as string[],
	selectedCategory: '',
	syncSingleForm: {
		dmbh: '',
	},
});

// 字典名称映射
const dictionaryNameMap = {
	XZBZ: '险种标志',
	YLTCLB: '医疗待遇类别',
	RQLB: '人群类别',
	YPBZ: '药品标志',
	XB: '性别',
	ZXBZ: '注销标志',
	SPBZ: '审批标志',
};

// 页面加载时
onMounted(async () => {
	await loadCategories();
	await handleQuery();
});

// 加载字典分类
const loadCategories = async () => {
	const response = await catalogApi.getDictionaryCategories();
	if (response.data?.result) {
		state.categories = response.data.result;
	}
};

// 查询操作
const handleQuery = async (params: any = {}) => {
	state.tableLoading = true;
	state.tableParams = Object.assign(state.tableParams, params);
	try {
		const result = await catalogApi.getDictionaryPaged(Object.assign(state.tableQueryParams, state.tableParams)).then((res) => res.data.result);
		state.tableParams.total = result?.total || 0;
		state.tableData = result?.items ?? [];
	} catch (error) {
	} finally {
		state.tableLoading = false;
	}
};

// 列排序
const sortChange = async (column: any) => {
	state.tableParams.field = column.prop;
	state.tableParams.order = column.order;
	await handleQuery();
};

// 选择分类
const handleSelectCategory = (category: string) => {
	state.selectedCategory = state.selectedCategory === category ? '' : category;
	state.tableQueryParams.dmBh = state.selectedCategory;
	handleQuery({ page: 1 });
};

// 同步常用字典
const handleSyncCommon = async () => {
	try {
		await ElMessageBox.confirm('此操作将同步所有常用数据字典，可能需要较长时间，是否继续？', '确认同步', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning',
		});

		state.syncCommonLoading = true;
		const response = await catalogApi.syncCommonDictionaries();
		if (response.data?.result) {
			ElMessage.success('同步完成');
			await loadCategories();
			await handleQuery();
		}
	} catch (error) {
	} finally {
		state.syncCommonLoading = false;
	}
};

// 同步指定字典
const handleSyncSingle = async () => {
	if (!state.syncSingleForm.dmbh) {
		ElMessage.warning('请输入字典编号');
		return;
	}

	try {
		state.syncSingleLoading = true;
		const response = await catalogApi.syncDictionary(state.syncSingleForm.dmbh);
		if (response.data?.result) {
			ElMessage.success('同步完成');
			await loadCategories();
			await handleQuery();
		}
	} catch (error) {
	} finally {
		state.syncSingleLoading = false;
	}
};

// 获取字典名称
const getDictionaryName = (code: string) => {
	return dictionaryNameMap[code as keyof typeof dictionaryNameMap] || code;
};

// 格式化日期
const formatDate = (dateStr: string) => {
	if (!dateStr) return '-';
	return new Date(dateStr).toLocaleString('zh-CN');
};
</script>

<template>
	<div class="dictionary-container">
		<!-- 字典分类 -->
		<el-card shadow="hover" class="mb15">
			<template #header>
				<div class="card-header">
					<span>字典分类</span>
					<el-button type="primary" size="small" @click="loadCategories">
						<el-icon><ele-Refresh /></el-icon>
						刷新分类
					</el-button>
				</div>
			</template>
			<div class="category-list">
				<el-tag
					v-for="category in state.categories"
					:key="category"
					:type="state.selectedCategory === category ? 'primary' : 'info'"
					:effect="state.selectedCategory === category ? 'dark' : 'plain'"
					class="category-tag"
					@click="handleSelectCategory(category)"
				>
					{{ getDictionaryName(category) }}
					<span class="category-code">({{ category }})</span>
				</el-tag>
			</div>
		</el-card>

		<el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
			<el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="80">
				<el-row>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="字典分类">
							<el-select v-model="state.tableQueryParams.dmBh" placeholder="请选择" clearable>
								<el-option v-for="category in state.categories" :key="category" :label="getDictionaryName(category)" :value="category" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item label="关键词">
							<el-input v-model="state.tableQueryParams.keyword" clearable placeholder="代码值、字典名称" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
						<el-form-item label="字典编号">
							<el-input v-model="state.syncSingleForm.dmbh" clearable placeholder="请输入字典编号" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
						<el-form-item>
							<el-button-group style="display: flex; align-items: center">
								<el-button type="primary" icon="ele-Search" @click="handleQuery" v-reclick="1000"> 查询 </el-button>
								<el-button icon="ele-Refresh" @click="() => (state.tableQueryParams = {})"> 重置 </el-button>
								<el-button icon="ele-ZoomIn" @click="() => (state.showAdvanceQueryUI = true)" v-if="!state.showAdvanceQueryUI" style="margin-left: 5px"> 高级查询 </el-button>
								<el-button icon="ele-ZoomOut" @click="() => (state.showAdvanceQueryUI = false)" v-if="state.showAdvanceQueryUI" style="margin-left: 5px"> 隐藏 </el-button>
								<el-button type="success" style="margin-left: 5px" icon="ele-Refresh" @click="handleSyncCommon" :loading="state.syncCommonLoading"> 同步常用字典 </el-button>
								<el-button type="warning" style="margin-left: 5px" icon="ele-Download" @click="handleSyncSingle" :loading="state.syncSingleLoading" v-if="state.showAdvanceQueryUI">
									同步指定字典
								</el-button>
							</el-button-group>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</el-card>
		<el-card class="full-table" shadow="hover" style="margin-top: 5px">
			<template #header>
				<span>字典数据</span>
				<span v-if="state.selectedCategory" class="selected-category"> 当前分类：{{ getDictionaryName(state.selectedCategory) }} </span>
			</template>
			<el-table
				:data="state.tableData"
				@selection-change="
					(val: any[]) => {
						state.selectData = val;
					}
				"
				style="width: 100%"
				v-loading="state.tableLoading"
				tooltip-effect="light"
				row-key="id"
				@sort-change="sortChange"
				border
			>
				<el-table-column type="selection" width="40" align="center" />
				<el-table-column type="index" label="序号" width="55" align="center" />
				<el-table-column prop="dmBh" label="代码编号" width="120" fixed="left" show-overflow-tooltip />
				<el-table-column prop="code" label="代码值" width="120" show-overflow-tooltip />
				<el-table-column prop="content" label="字典名称" width="200" show-overflow-tooltip />
				<el-table-column prop="lastSyncTime" label="最后同步时间" width="160" sortable="custom">
					<template #default="{ row }">
						<span>{{ formatDate(row.lastSyncTime) }}</span>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				v-model:currentPage="state.tableParams.page"
				v-model:page-size="state.tableParams.pageSize"
				@size-change="(val: any) => handleQuery({ pageSize: val })"
				@current-change="(val: any) => handleQuery({ page: val })"
				layout="total, sizes, prev, pager, next, jumper"
				:page-sizes="[10, 20, 50, 100, 200, 500]"
				:total="state.tableParams.total"
				size="small"
				background
			/>
		</el-card>
	</div>
</template>

<style lang="scss" scoped>
.dictionary-container {
	padding: 15px;

	.card-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.category-list {
		display: flex;
		flex-wrap: wrap;
		gap: 8px;

		.category-tag {
			cursor: pointer;
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-1px);
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
			}

			.category-code {
				font-size: 12px;
				opacity: 0.8;
				margin-left: 4px;
			}
		}
	}

	.selected-category {
		float: right;
		color: #409eff;
		font-size: 14px;
		font-weight: 500;
	}
}

:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>

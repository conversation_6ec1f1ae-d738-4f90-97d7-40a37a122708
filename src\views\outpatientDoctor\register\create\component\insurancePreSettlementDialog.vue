<template>
	<div class="charge-container">
		<el-dialog v-model="state.isShowDialog" :width="700" draggable="" :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<!--<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>-->
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules"
				v-loading="state.loading">
				<el-row :gutter="15">

					<!-- 患者信息 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="姓名" prop="patientName">
							<el-input v-model="state.register.patientName" disabled>
								<template #append>{{ state.register.sex == 1 ? '男' : '女' }}</template>
							</el-input>
						</el-form-item>
					</el-col>


					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="医保卡号" prop="kh">
							<el-input v-model="state.settleMzPreRequest.kh" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="身份证号码" prop="idCardNo">
							<el-input v-model="state.register.idCardNo" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="单位名称" prop="dwmc">
							<el-input v-model="state.settleMzPreRequest.dwmc" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="经办机构" prop="sbjgbh">
							<el-input v-model="state.settleMzPreRequest.sbjgbh" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="账户余额" prop="zhye">
							<el-input v-model="state.settleMzPreRequest.zhye" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="家庭共济账户余额" prop="gjzhye">
							<el-input v-model="state.ruleForm.gjzhye" disabled>
								<template #append>
									<el-button type="primary">获取共济余额</el-button>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<!-- 
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="补充人员类别" prop="companyName">
							<el-input v-model="state.ruleForm.companyName" disabled />
						</el-form-item>
					</el-col> -->

					<!-- 费用信息 -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label=" 本次结算费用总额" prop="zje">
							<el-input v-model="state.ruleForm.zje" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本次进入统筹额度" prop="bcnrtcfw">
							<el-input v-model="state.ruleForm.bcnrtcfw" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本年进入统筹额度" prop="bnynrtcfw">
							<el-input v-model="state.ruleForm.bnynrtcfw" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本次统筹支付" prop="tczf">
							<el-input v-model="state.ruleForm.tczf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="累计统筹支付" prop="ljtczf">
							<el-input v-model="state.ruleForm.ljtczf" disabled />
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本年度累计门诊大病报销金额" prop="bnmzdbljbxje">
							<el-input v-model="state.ruleForm.bnmzdbljbxje" disabled />
						</el-form-item>
					</el-col> -->
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="重复取药提示（聊城：慢性病30天内重复取药时返回）" prop="cfqyts">
							<el-input v-model="state.ruleForm.pkryzjzje" cfqyts />
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本次起付线" prop="bcqfx">
							<el-input v-model="state.ruleForm.bcqfx" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="医疗补助金额" prop="ylbzje">
							<el-input v-model="state.ruleForm.ylbzje" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="优抚对象减免金额" prop="jmje">
							<el-input v-model="state.ruleForm.jmje" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本次大额支付" prop="dezf">
							<el-input v-model="state.ruleForm.dezf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="累计大额支付" prop="ljdezf">
							<el-input v-model="state.ruleForm.ljdezf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="累计门诊额度" prop="ljmzed">
							<el-input v-model="state.ruleForm.ljmzed" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="累积个人支付" prop="ljgrzf">
							<el-input v-model="state.ruleForm.ljgrzf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="大额商业保险" prop="desybx">
							<el-input v-model="state.ruleForm.desybx" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本次公务员补助" prop="gwybz">
							<el-input v-model="state.ruleForm.gwybz" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="其他统筹支付" prop="qttczf">
							<el-input v-model="state.ruleForm.qttczf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="医疗补助金额" prop="ylbzje">
							<el-input v-model="state.ruleForm.ylbzje" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="*病人负担金额" prop="brfdje">
							<el-input v-model="state.ruleForm.brfdje" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="个人账户支付" prop="grzhzf">
							<el-input v-model="state.ruleForm.grzhzf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="医疗减免金额" prop="companyName">
							<el-input v-model="state.ruleForm.companyName" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="本次财政列支" prop="czlz">
							<el-input v-model="state.ruleForm.czlz" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="暂缓支付" prop="zhzf">
							<el-input v-model="state.ruleForm.zhzf" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="参保地扣卡" prop="companyName">
							<el-input v-model="state.ruleForm.companyName" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="家庭共济支付" prop="medicalAccountPayment">
							<el-input v-model="state.ruleForm.medicalAccountPayment" disabled />
						</el-form-item>
					</el-col>


					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="医保负担金额" prop="ybfdje">
							<el-input v-model="state.ruleForm.ybfdje" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="医疗统筹登记号" prop="yltcdjh">
							<el-input v-model="state.ruleForm.yltcdjh" disabled />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="院内账户支付" prop="familyAccountBalance">
							<el-input v-model="state.ruleForm.familyAccountBalance" disabled />
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="发票号" prop="fph">
							<el-input v-model="state.ruleForm.fph" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="发票人员类别" prop="fprylb">
							<el-input v-model="state.ruleForm.fprylb" disabled />
						</el-form-item>
					</el-col> -->

				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">

					<el-button @click="cancel" :disabled="state.loading">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">医保结算</el-button>

				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
<script lang="ts" setup name="charge">
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';
import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { useRegisterApi } from '/@/api/outpatientDoctor/register';
import { useBaseApi } from '/@/api/base';
import {
	InsuranceRegister,
	SettleMzPreRequest,
	SettleMzPreBcxm,
	SettleMzPreJbbm,
	SettleMzPreFypd,
	SettleMzRequest
} from '/@/models/insurance';
// API服务
const insurancePatientApi = useBaseApi("insurancePatient");
const insuranceOutpatientApi = useBaseApi("insuranceOutpatient");
const basicInfoApi = useBasicInfoApi();
const registerApi = useRegisterApi();
//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});

//父级传递来的函数，用于回调
const emit = defineEmits(['settlementFinish']);
const ruleFormRef = ref();
const state = reactive({
	loading: false,
	icd10Loading: false,
	icd10Data: [] as Array<any>,
	isShowDialog: false,
	paymentMethodData: [] as Array<any>,
	ruleForm: {
		acquisitionMethod: 2, // 默认有卡
	} as any,
	register: {} as any, //挂号信息
	settleMzPreRequest: {} as any, //预结算请求参数
});
//自行添加其他规则
const rules = ref<FormRules>({
	paymentMethod: [{ required: true, message: '请选择支付方式！', trigger: 'change' }],
});

// 打开弹窗
//// @ts-ignore 
const openDialog = async (insuranceRegister: InsuranceRegister, settleMzPreRequest: SettleMzPreRequest) => {


	try {
		state.isShowDialog = true;
		console.log("医保结算：", insuranceRegister, settleMzPreRequest);
		state.register = insuranceRegister;
		state.settleMzPreRequest = settleMzPreRequest;
		ruleFormRef.value?.resetFields();
		state.register = insuranceRegister;
		state.loading = true;

		console.log('医保预结算参数：', settleMzPreRequest);
		await insuranceOutpatientApi.post("settlePre", {
			chargeId: [insuranceRegister.chargeId],
			settleMzPreRequest: settleMzPreRequest,
		}).then((res) => {
			if (res.data.type == "success") {
				Object.assign(state.ruleForm, res.data.result);
				console.log('医保预结算结果：', res.data.result);
			} else {
				ElMessage.error(res.data.message);
			}
		}).catch((error) => {
			ElMessage.error('加载预结算失败：' + error);
		}).finally(() => {
			state.loading = false;
		});
		// 获取卡余额



	} catch (error) {

		ElMessage.error('加载预结算失败：' + error);
	} finally {
		// 无论成功还是失败，都关闭加载状态
		state.loading = false;
	}
};
const getSettlePreParams = (register: any, insuranceRegister: any) => {
	// 获取预结算参数
	let params = {
		registerId: register.id,
		billingDeptId: register.deptId,
		billingDoctorId: register.doctorId,
		executeDeptId: register.deptId,
		executeDoctorId: register.doctorId,
		SettleMzPreRequest: {} as any,
	};
	// 遍历insuranceRegister的所有属性，添加前缀p_，并赋值给SettleMzPreRequest
	for (const key in insuranceRegister) {
		if (insuranceRegister.hasOwnProperty(key)) {
			const newKey = 'p_' + key;
			params.SettleMzPreRequest[newKey] = insuranceRegister[key];
		}
	}
	return params;
}

// icd10
const icd10RemoteMethod = async (query: string) => {
	const res = await basicInfoApi.getIcd10s({ keyword: query });
	state.icd10Data = (res.data.result ?? []).map((item: any) => ({ code: item.code, name: item.name }));
};
//选择诊断后
const diagnosticCodeChange = (value: any, name: any) => {
	var obj = state.icd10Data.find((item) => {
		//数据源
		return item.code === value;
	});
	state.ruleForm.diagName = obj?.name ?? value;
};

// 关闭弹窗
const closeDialog = () => {

	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	closeDialog();
};

// 字典对照
const dictMap = {
	XB: { 1: '男', 2: '女', 9: '不确定' },
	RQLB: { 'A': '职工', 'B': '居民' },
	YDBZ: {
		0: '非异地', 1: '异地'
	},
	PKRK: { 0: '非贫困人口', 1: '贫困人口' },
	ZFBZ: { 0: '灰名单', 1: '白名单' },
	// 可扩展其他字段...
} as any;
const getLabelByDict = (dictType: any, value: string | number) => {
	const dict = dictMap[dictType];
	return dict ? dict[value] || '' : '';
};
// 远程获取
//const dictMap = ref<Record<string, Record<string, string>>>({}); // 存储字典数据

// 获取数据字典
// const loadDict = async () => {
// 	const res = await basicInfoApi.getDicts({ codes: ['XB', 'RQLB'] }); // 接口示例
// 	res.data.result.forEach((item: { code: string; items: Array<{ value: string; label: string }> }) => {
// 		dictMap.value[item.code] = item.items.reduce((acc, cur) => {
// 			acc[cur.value] = cur.label;
// 			return acc;
// 		}, {} as Record<string, string>);
// 	});
// };
// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			try {
				// 显示加载状态
				state.loading = true;

				// 设置要提交的数据
				state.register.paymentMethod = state.ruleForm.paymentMethod;
				state.register.totalAmount = state.ruleForm.totalAmount;
				state.register.personalPayment = state.ruleForm.personalPayment;

				// 提交数据
				await registerApi.add(state.register)
					.then((res) => {
						// 提交成功，显示成功消息
						if (res.data.type == "success") {
							ElMessage.success('收费成功');
							emit('resetForm', res.data.result);
							closeDialog();
						} else {
							ElMessage.error(res.data.message);
						}

					})



			} catch (error) {
				console.error('收费失败:', error);
				ElMessage.error('收费失败，请重试');
			} finally {
				// 无论成功还是失败，都关闭加载状态
				state.loading = false;
			}
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};
///
const getPatientInfo = async () => {

	console.log('获取医保患者信息 state.ruleForm.acquisitionMethod=', state.ruleForm.acquisitionMethod);
	if (state.ruleForm.acquisitionMethod === 1) {
		// 有卡
		state.ruleForm.sbjgmc = state.register.insurance.medicalInstitutionCode;
		state.ruleForm.sbjgbh = state.register.insurance.medicalInsuranceCardNumber;
	} else if (state.ruleForm.acquisitionMethod === 2) {
		// 无卡
		state.loading = true;
		await insurancePatientApi.post("QueryBasicInfo", {
			p_grbh: state.ruleForm.idCardNo,
			p_xzbz: state.ruleForm.p_xzbz,
			p_xm: state.register.name,
			p_yltclb: state.ruleForm.p_yltclb
		}).then((res) => {
			Object.assign(state.ruleForm, res.data.result);
			console.log(res.data.result, state.ruleForm);
		}).finally(() => {
			// 关闭加载状态
			state.loading = false;
		});
	} else if (state.ruleForm.acquisitionMethod === 3) {
		// 电子凭证
		state.ruleForm.sbjgmc = state.register.insurance.medicalInstitutionCode;
		state.ruleForm.sbjgbh = state.register.insurance.medicalInsuranceCardNumber;
	} else if (state.ruleForm.acquisitionMethod === 4) {
		// 刷脸
		state.ruleForm.sbjgmc = '';
		state.ruleForm.sbjgbh = '';
	}


};
///预结算参数
export interface InsuranceSettleMzPreParams {
	p_yltclb: string; // 医疗统筹类别
	idCardNo: string;
	insurance: {
		medicalPoolingCategory: string;
		medicalInsuranceFlag: string;
		medicalInstitutionCode: string;
		medicalInsuranceCardNumber: string;
	};
	name: string;
	acquisitionMethod?: string; // 认证方式
	insurancePatientInfo?: any; // 医保患者信息
	// 诊断
	diagnosticCode?: string; // 诊断代码
	diagnosticName?: string; // 诊断名称
	chargeId?: number;
	registerId: number;
	chargeList: any[]; // 收费类型
}
//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>

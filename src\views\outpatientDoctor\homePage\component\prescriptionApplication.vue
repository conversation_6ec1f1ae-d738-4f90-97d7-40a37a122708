<template>
	<div class="prescriptionApplication-container">
		<el-container style="height: calc(100%)">
			<el-container>
				<el-aside width="250px" style="height: 100%; height: 100%; position: relative" tooltip-effect="light">
					<div
						style="height: 40px; width: 100%; background-color: #f8f8f8; border-radius: 2px; border: 1px solid #f0f0f0; padding: 7px 10px">
						<el-link style="font-size: 16px" type="primary">处方列表</el-link>
						<el-button text icon="ele-Refresh" type="warning" @click="queryMain" size="small"
							style="float: right">刷新</el-button>
					</div>
					<div class="list-wrapper" v-loading="state.loading.mainLoading">
						<!-- v-model:selectedKey="state.currentPrescription.id" -->
						<el-ant-list v-if="state.mainTableData.length > 0" :data="state.mainTableData"
							:showAvatar="false" @select="onSelectPrescription">
							<template #title="{ item }">
								<el-tag :type="item.outpatientPrescriptionTagType">
									{{ item.outpatientPrescriptionTypeName }}
								</el-tag>
							</template>
							<template #description="{ item }">
								<!-- <el-tag type="warning"> -->
								{{ item.diagnosticName }}
								{{ item.tcmDiagnosticName }}
								<!-- </el-tag> -->
							</template>

							<template #extra="{ item }">
								<span style="font-size: 12px"> {{ item.billingDeptName }}</span>
							</template>

							<template #status="{ item }">
								<!-- 状态 0未审核1未收费2已收费3已取药4已退药5已退费6作废-->
								<el-tag type="success" v-if="item.status == 0"> 待审核 </el-tag>
								<el-tag type="info" v-else-if="item.status == 1"> 待收费 </el-tag>
								<el-tag type="warning" v-else-if="item.status == 2"> 已收费 </el-tag>
								<el-tag type="danger" v-else-if="item.status == 3"> 已取药 </el-tag>
							</template>

							<template #footer="{ item }">
								<span> {{ item.prescriptionTime }}</span>
							</template>
						</el-ant-list>
					</div>
				</el-aside>
				<el-main style="padding: 0px 0px 0px 5px">
					<el-card shadow="hover" style="height: 100%" :body-style="{ padding: '0px' }">
						<el-form label-width="auto" :inline="true" :model="state.currentPrescription">
							<el-descriptions :label-width="100" :column="4" border>
								<el-descriptions-item label="处方类型" label-align="right">
									<g-sys-dict v-model="state.currentPrescription.outpatientPrescriptionType"
										@change="prescTypeChange" render-as="select" clearable filterable
										code="OutpatientPrescriptionType" />
								</el-descriptions-item>
								<el-descriptions-item label="处方号" label-align="right">
									<span v-if="state.currentPrescription.id == 0">待生成</span>
									<span v-else>{{ state.currentPrescription.prescriptionNo ?? '待生成' }}</span>
								</el-descriptions-item>
								<el-descriptions-item label="处方时间" label-align="right">
									<el-date-picker v-model="state.currentPrescription.prescriptionTime" disabled
										type="datetime" placeholder="入院时间" />
								</el-descriptions-item>

								<el-descriptions-item label="主诊断" label-align="right"
									v-if="state.isHerbPrescription == false">
									<el-select filterable v-model="state.currentPrescription.diagnosticCode"
										:disabled="isReadOnly" remote reserve-keyword :remote-method="icd10RemoteMethod"
										:loading="state.loading.icd10Loading"
										@change="(val: any) => diagnosticCodeChange(val, 'diagnosticName')"
										placeholder="请选择诊断">
										<el-option v-if="state.icd10Data.length == 0"
											:value="state.currentPrescription.diagnosticCode ?? ''"
											:label="state.currentPrescription.diagnosticName ?? ''"> </el-option>
										<el-option v-else v-for="item in state.icd10Data" :key="item.code"
											:label="item.name" :value="item.code" />
									</el-select>
								</el-descriptions-item>
								<el-descriptions-item label="次诊断1" label-align="right" align="center"
									v-if="state.isHerbPrescription == false">
									<el-select filterable v-model="state.currentPrescription.diagnostic1Code" remote
										reserve-keyword :disabled="isReadOnly" :remote-method="icd10RemoteMethod"
										:loading="state.loading.icd10Loading"
										@change="(val: any) => diagnosticCodeChange(val, 'diagnostic1Name')"
										placeholder="请选择诊断">
										<el-option v-if="state.icd10Data.length == 0"
											:value="state.currentPrescription.diagnostic1Code ?? ''"
											:label="state.currentPrescription.diagnostic1Name ?? ''"> </el-option>
										<el-option v-else v-for="item in state.icd10Data" :key="item.code"
											:label="item.name" :value="item.code" />
									</el-select>
								</el-descriptions-item>
								<el-descriptions-item label="次诊断2" label-align="right" align="center"
									v-if="state.isHerbPrescription == false">
									<el-select filterable v-model="state.currentPrescription.diagnostic2Code" remote
										reserve-keyword :disabled="isReadOnly" :remote-method="icd10RemoteMethod"
										:loading="state.loading.icd10Loading"
										@change="(val: any) => diagnosticCodeChange(val, 'diagnostic2Name')"
										placeholder="请选择诊断">
										<el-option v-if="state.icd10Data.length == 0"
											:value="state.currentPrescription.diagnostic2Code ?? ''"
											:label="state.currentPrescription.diagnostic2Name ?? ''"> </el-option>
										<el-option v-else v-for="item in state.icd10Data" :key="item.code"
											:label="item.name" :value="item.code" />
									</el-select>
								</el-descriptions-item>
								<el-descriptions-item label="中医诊断" label-align="right"
									v-if="state.isHerbPrescription == true">
									<el-select allow-create default-first-option filterable
										v-model="state.currentPrescription.tcmDiagnosticCode" :disabled="isReadOnly"
										remote reserve-keyword
										:remote-method="(query: any) => tcmDiagnosticRemoteMethod(query)"
										:loading="state.loading.icd10Loading"
										@change="(val: any) => diagnosticCodeChange(val, 'tcmDiagnosticName')"
										placeholder="请选择中医诊断">
										<el-option v-if="state.icd10Data.length == 0"
											:value="state.currentPrescription.tcmDiagnosticCode ?? ''"
											:label="state.currentPrescription.tcmDiagnosticName ?? ''"> </el-option>
										<el-option v-else v-for="item in state.icd10Data" :key="item.code"
											:label="item.name" :value="item.code" />
									</el-select>
								</el-descriptions-item>

								<el-descriptions-item label="中医证型" label-align="right" align="center"
									v-if="state.isHerbPrescription == true">
									<el-select filterable v-model="state.currentPrescription.tcmSyndromeCode" remote
										reserve-keyword :disabled="isReadOnly"
										:remote-method="(query: any) => tcmSyndromeCodeRemoteMethod(query)"
										:loading="state.loading.icd10Loading"
										@change="(val: any) => diagnosticCodeChange(val, 'tcmSyndromeName')"
										placeholder="请选择中医证型">
										<el-option v-if="state.icd10Data.length == 0"
											:value="state.currentPrescription.tcmSyndromeCode ?? ''"
											:label="state.currentPrescription.tcmSyndromeName ?? ''"> </el-option>
										<el-option v-else v-for="item in state.icd10Data" :key="item.code"
											:label="item.name" :value="item.code" />
									</el-select>
								</el-descriptions-item>
								<el-descriptions-item label="处方说明" label-align="right">
									<el-input :disabled="isReadOnly" v-model="state.currentPrescription.remark"
										placeholder="请输入处方说明" show-word-limit clearable />
								</el-descriptions-item>
								<el-descriptions-item label="付数" label-align="right" align="center"
									v-if="state.isHerbPrescription == true">
									<el-input-number :disabled="isReadOnly" filterable
										v-model="state.currentPrescription.herbsQuantity" placeholder="请输入付数">
									</el-input-number>
								</el-descriptions-item>

								<el-descriptions-item label="是否代煎" label-align="right"
									v-if="state.isHerbPrescription == true">
									<el-switch :disabled="isReadOnly" v-model="state.currentPrescription.isDecoction"
										inline-prompt active-text="是" inactive-text="否" :active-value="1"
										:inactive-value="0" />
								</el-descriptions-item>
								<el-descriptions-item label="煎药方式" label-align="right"
									v-if="state.isHerbPrescription == true && state.currentPrescription.isDecoction == 1">
									<g-sys-dict :disabled="isReadOnly"
										v-model="state.currentPrescription.herbsDecoction" render-as="select" clearable
										filterable code="DecoctionMethod" />
								</el-descriptions-item>
							</el-descriptions>

						</el-form>
						<el-form :model="state.currentDrug" ref="ruleFormRef" label-width="auto">
							<el-row :gutter="5">
								<el-form-item v-show="false">
									<el-input v-model="state.currentDrug.id" />
								</el-form-item>
								<el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4" class="mb10">
									<el-form-item label="药品类型" prop="drugType">
										<el-select v-model="state.drugSearchParam.drugType" :disabled="isReadOnly">
											<el-option label="西药" :value="'1'" />
											<el-option label="中成药" :value="'2'" />
											<el-option label="中药饮片" :value="'3'" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
									<el-form-item label="药品检索" prop="drugName" class="mb10">
										<el-input v-model="state.currentDrug.drugName" placeholder="请输入药品名称检索" clearable
											:disabled="isReadOnly" ref="drugInputRef" style="margin-bottom: 0px; "
											@keydown.enter="(event: any) => triggerSearch(event, 0)">

										</el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4" class="mb10">
									<el-form-item label="用药天数" prop="medicationDays">
										<el-input ref="dayInputRef" v-model="state.currentDrug.medicationDays"
											:disabled="isReadOnly"
											@keydown.enter="(event: any) => nextInput(event, 'dayInputRef')"
											placeholder="请输用药天数" clearable />
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4" class="mb10">
									<el-form-item label="单次剂量" prop="singleDose">

										<el-input ref="singleDoseInputRef" v-model="state.currentDrug.singleDose"
											:min="0" placeholder="" type="number" :disabled="isReadOnly"
											@keydown.enter="(event: any) => nextInput(event, 'singleDoseInputRef')"
											controls-position="right" @input="calculateQuantity(state.currentDrug)">
											<template #append>
												{{ state.currentDrug.singleDoseUnit }}
											</template>
										</el-input>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4" class="mb10">
									<el-form-item label="用药频次" prop="frequencyId">
										<el-select ref="frequencyInputRef" clearable filterable
											v-model="state.currentDrug.frequencyId" placeholder="频次"
											:disabled="isReadOnly" @change="freqChange">
											<el-option v-for="(item, index) in state.frequencyList" :key="index"
												:value="item.id" :label="item.name" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4" class="mb10">
									<el-form-item label="给药途径" prop="medicationRoutesId">
										<el-select ref="routeInputRef" clearable filterable
											v-model="state.currentDrug.medicationRoutesId" @change="routeChange"
											:disabled="isReadOnly" placeholder="途径">
											<el-option v-for="(item, index) in state.routeList" :key="index"
												:value="item.id" :label="item.routeName" />
										</el-select>
									</el-form-item>
								</el-col>
								<el-col v-if="!state.isHerbPrescription" :xs="24" :sm="12" :md="4" :lg="4" :xl="4"
									class="mb10">
									<el-form-item label="用法" prop="medicationMethod">
										<g-sys-dict :ref="usageInputRef" v-model="state.currentDrug.usageName"
											@keydown.enter="(event: any) => nextInput(event, 'usageInputRef')"
											render-as="select" clearable filterable :disabled="isReadOnly"
											code="DrugUsageType" />
									</el-form-item>
								</el-col>
								<el-col v-if="state.isHerbPrescription" :xs="24" :sm="12" :md="4" :lg="4" :xl="4"
									class="mb10">
									<el-form-item label="煎服法" prop="usageName">
										<g-sys-dict ref="usageInputRef1" v-model="state.currentDrug.usageName"
											@keydown.enter="(event: any) => nextInput(event, 'usageInputRef')"
											render-as="select" clearable filterable :disabled="isReadOnly"
											code="HerbDecoctionType" />
									</el-form-item>

								</el-col>


								<el-col v-if="!state.isHerbPrescription" :xs="24" :sm="12" :md="4" :lg="4" :xl="4"
									class="mb10">
									<el-form-item label="抗生素用药方式" prop="medicationMethod">
										<el-select ef="medicationMethodInputRef"
											@keydown.enter="(event: any) => nextInput(event, 'medicationMethodInputRef')"
											v-model="state.currentDrug.medicationMethod" placeholder="请选择"
											:disabled="Number(state.currentDrug.antibacterialLevel) == 0 || isReadOnly">
											<el-option label="治疗用药" :value="1" />
											<el-option label="预防用药" :value="2" />

										</el-select>
									</el-form-item>
								</el-col>
								<el-col v-if="!state.isHerbPrescription" :xs="24" :sm="12" :md="4" :lg="4" :xl="4"
									class="mb10">
									<el-form-item label="是否皮试" prop="isSkinTest">
										<el-switch v-model="state.currentDrug.isSkinTest" :active-value="1"
											:inactive-value="2" disabled size="small" />
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>


						<DrugSearchV1 ref="drugSearchRef" @select="handleDrugSelect" style="margin-top: 0px"
							:width="'50%'" :drugType="state.drugSearchParam.drugType"
							:storageId="state.drugSearchParam.storageId"
							:drugTypeInclude="state.drugSearchParam.drugTypeInclude"
							:searchKeyword="state.drugSearchParam.keyword" />
						<el-table ref="detailTableRef" :data="state.detailTableData" style="width: 100%"
							v-loading="state.loading.detailLoading" tooltip-effect="light" row-key="drugCode" border
							@selection-change="
								(val) => {
									state.detailSelectData = val;
								}
							">
							<el-table-column type="selection" width="30" />
							<!-- <el-table-column type="index" label="序号" width="50" align="center" /> -->
							<el-table-column prop="groupFlag" v-if="!state.isHerbPrescription" fixed="left" label="组"
								show-overflow-tooltip width="30" />
							<!-- <el-table-column prop="drugCode" label="药品编码" show-overflow-tooltip /> -->
							<el-table-column class="drugNameColumn" column-key="drugName" prop="drugName" fixed="left"
								label="药品名称" width="200">
								<template #default="scope">
									<el-input v-model="scope.row.drugName" placeholder="请输入药品名称检索" clearable
										style="margin-bottom: 0px; "
										@keydown.enter="(event: any) => triggerSearch(event, scope.$index)">

									</el-input>
								</template>
							</el-table-column>

							<el-table-column prop="spec" label="规格" show-overflow-tooltip />
							<el-table-column prop="unit" label="单位" show-overflow-tooltip width="50" align="center" />
							<el-table-column prop="medicationDays" column-key="medicationDays" label="天数"
								show-overflow-tooltip width="60">

							</el-table-column>
							<el-table-column v-if="!state.isHerbPrescription" prop="singleDose" label="单次剂量" width="120"
								show-overflow-tooltip>

							</el-table-column>
							<el-table-column v-if="state.isHerbPrescription" prop="singleDose" label="单付剂量" width="120"
								show-overflow-tooltip>

							</el-table-column>
							<el-table-column prop="frequencyName" label="频次" width="100" show-overflow-tooltip>

							</el-table-column>
							<el-table-column prop="medicationRoutesName" label="途径" width="100" show-overflow-tooltip>

							</el-table-column>

							<el-table-column prop="quantity" label="数量" width="60" show-overflow-tooltip>
								<template #default="scope"> {{ scope.row.quantity }} {{ scope.row.unit }} </template>
							</el-table-column>

							<el-table-column prop="price" label="单价" show-overflow-tooltip align="right">
								<template #default="scope">
									{{ formatNumber(scope.row.price) }}
								</template>
							</el-table-column>
							<el-table-column prop="amount" label="金额" show-overflow-tooltip align="right">
								<template #default="scope">
									{{ formatNumber(scope.row.amount) }}
								</template>
							</el-table-column>
							<el-table-column v-if="!state.isHerbPrescription" prop="usageName" label="用法"
								show-overflow-tooltip>
								<template #default="scope">
									<g-sys-dict v-model="scope.row.usageName" render-as="select" clearable filterable
										:disabled="true" code="DrugUsageType" />
								</template>
							</el-table-column>
							<el-table-column v-if="!state.isHerbPrescription" prop="medicationMethod" label="抗生素用药方式"
								width="130" show-overflow-tooltip>
								<template #default="scope">
									<span v-if="scope.row.medicationMethod === 1"> 治疗用药</span>
									<span v-if="scope.row.medicationMethod === 2"> 预防用药</span>

								</template>
							</el-table-column>

							<el-table-column v-if="state.isHerbPrescription" prop="usageName" label="煎服法"
								show-overflow-tooltip>
								<template #default="scope">
									<g-sys-dict v-model="scope.row.usageName" render-as="select" clearable filterable
										:disabled="true" code="HerbDecoctionType" />
								</template>
							</el-table-column>
							<el-table-column v-if="!state.isHerbPrescription" prop="isSkinTest" label="是否皮试"
								show-overflow-tooltip>
								<template #default="scope">
									<el-switch v-model="scope.row.isSkinTest" :active-value="1" :inactive-value="2"
										disabled size="small" />
								</template>
							</el-table-column>
							<el-table-column v-if="!state.isHerbPrescription" prop="skinTestResults" label="皮试结果"
								show-overflow-tooltip />
							<el-table-column prop="selfPayRatio" label="自付比例" show-overflow-tooltip />
							<el-table-column prop="isRatioAudit" label="是否审核" show-overflow-tooltip>
								<template #default="scope">
									<g-sys-dict v-model="scope.row.isRatioAudit" code="YesNoEnum" />
								</template>
							</el-table-column>
							<el-table-column prop="storageName" label="药房" show-overflow-tooltip />

							<el-table-column prop="medicineCode" label="国家医保编码" width="100" show-overflow-tooltip />
							<el-table-column prop="manufacturerName" label="生产厂家" show-overflow-tooltip />

							<el-table-column label="删除" width="45" align="center" fixed="right" show-overflow-tooltip>
								<template #default="scope">
									<el-button icon="ele-Delete" size="small" text type="primary"
										@click.prevent="delPrescriptionDetail(scope.row)"> </el-button>
								</template>
							</el-table-column>
						</el-table>
					</el-card>
				</el-main>
			</el-container>
			<el-footer style="padding: 0px; margin-top: 5px">
				<el-card shadow="hover" style="width: 100%; height: 100%">
					<div class="button-container">
						<el-button-group>
							<el-button type="info" @click="delPrescriptionMain" v-reclick="5000" icon="ele-Delete"
								:disabled="state.currentPrescription.status != 1">删除处方</el-button>

							<el-button type="success" icon="ele-Reading"
								@click="templateDialogRef.openDialog(state, '组套')">组套</el-button>

							<el-button type="success"
								:disabled="state.currentPrescriptionStatus != 'edit' || state.currentPrescription.status > 1"
								@click="createGroup" icon="ele-CirclePlus">成组</el-button>
							<el-button type="primary" icon="ele-Edit" @click="addNew" v-reclick="5000">新方</el-button>
							<el-button type="warning" icon="ele-Finished" @click="savePrescription"
								:disabled="state.currentPrescriptionStatus != 'edit' || state.currentPrescription.status > 1"
								v-reclick="5000">保存</el-button>
							<!-- v-if="(state.currentPrescriptionStatus == 'view' && state.currentPrescription.id != 0 &&
							state.currentPrescription.status == 1)" -->
							<el-button type="warning" icon="ele-Finished"
								@click="state.currentPrescriptionStatus = 'edit'"
								:disabled="state.currentPrescriptionStatus == 'edit' || state.currentPrescription.status > 1 || state.currentPrescription.id == 0"
								v-reclick="5000">修改</el-button>

							<el-button type="info" icon="ele-Printer" v-reclick="5000">打印</el-button>
							<el-button type="info" icon="ele-View">打印预览</el-button>
						</el-button-group>
					</div>
				</el-card>
			</el-footer>
		</el-container>
		<!-- <el-row style=" height:	calc(100% - 60px);">
			<el-col :span="8">
	 
			</el-col>
			<el-col :span="16">

			</el-col>
		</el-row>
		<el-row style="margin-top: auto" class="foot">
			<el-col :span="24">
				<el-card style="margin-top: 5px;" :body-style="{ padding: '0px', height: '50px' }" shadow="never">



				</el-card>
			</el-col>
		</el-row> -->
		<templateDialog ref="templateDialogRef" @closeDialogWithParams="onSelectTemplate" />
	</div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage, ElTable } from 'element-plus';

import { RegisterOutput } from '/@/api-modules/outpatientDoctor/models';

// 药品检索
import DrugSearchV1 from '/@/views/pharmacy/drugInventory/component/drugSearchOfPrescription.vue';
import { formatNumber } from '/@/utils/formatObject';

import ElAntList from '/@/components/list/elAntList.vue';
import { usePrescriptionApi } from '/@/api/outpatientDoctor/prescription';
import { usePrescriptionApplication } from './usePrescriptionApplication';
import templateDialog from './templatePrescription.vue';

const templateDialogRef = ref();
const prescriptionApi = usePrescriptionApi();
const {
	initData,
	state,
	tcmDiagnosticRemoteMethod,
	tcmSyndromeCodeRemoteMethod,
	icd10RemoteMethod,
	checkDrugPermission, // 药品权限验证
	queryPrescriptionList,
	delPrescriptionDetail,
	savePrescription,
	addNew,
	calculateQuantity,
	setMedicationRoutes,
	createPrescriptionGroup,
	onSelectPrescription, // 选中处方列表后
	diagnosticCodeChange, // 诊断选中事件
	prescTypeChange, // 处方类型选中事件
} = usePrescriptionApplication();

//父级传递来的参数
var props = defineProps({
	patientInfo: {
		type: Object as () => RegisterOutput, // 使用 Object 和工厂函数指定类型
		required: true,
	},
});
watch(
	() => props.patientInfo,
	async (val: any) => {
		console.log('open patientInfo', props.patientInfo, val);
		state.currentPatientInfo = props.patientInfo;
		queryMain();
	},
	{ deep: true } //启用深度监听
);

// 控制控件是否只读
const isReadOnly = computed(() => state.currentPrescriptionStatus === 'view');
//药品检索
const drugSearchRef = ref<InstanceType<typeof DrugSearchV1>>();
const triggerSearch = (event: KeyboardEvent, rowIndex: number) => {
	if (drugSearchRef.value) {
		drugSearchRef.value.triggerEnterKey(rowIndex);
	} else {
		console.warn('drugSearchRef is not yet available');
	}
};
const drugInputRef = ref();
const dayInputRef = ref();
const singleDoseInputRef = ref();
const frequencyInputRef = ref();
const routeInputRef = ref();
const usageInputRef = ref();
const medicationMethodInputRef = ref();

const nextInput = (el: any, inputName: string) => {
	switch (inputName) {
		case 'drugInputRef':
			dayInputRef.value.focus();
			break;
		case 'dayInputRef':
			routeInputRef.value.focus();
			break;
		case 'singleDoseInputRef':
			frequencyInputRef.value.focus();
			break;
		case 'frequencyInputRef':
			routeInputRef.value.focus();
			break;
		case 'routeInputRef':
			usageInputRef.value.focus();
			break;
		case 'usageInputRef':
			medicationMethodInputRef.value.focus();
	}

}


const freqChange = (value: any) => {
	setMedicationRoutes(state.currentDrug)
	nextInput(event, 'frequencyInputRef')

	//calculateQuantity(state.currentDrug)
	nextTick(() => {
		debugger;
		if (routeInputRef.value) {
			routeInputRef.value.focus();
		}
	});

};
const routeChange = (value: any) => {
	setMedicationRoutes(state.currentDrug)
	nextInput(event, 'routeInputRef')
};
//用于获取焦点

// 选择药品后
const handleDrugSelect = (selectedDrug: any) => {

	console.log('selectedDrug', selectedDrug);
	// 检查 state.tableData 中是否已经存在相同 drugCode 的药品
	const exists = state.detailTableData.some((drug) => drug.drugCode === selectedDrug.drugCode);

	if (exists) {
		ElMessage({
			message: `药品编码 ${selectedDrug.drugCode} 已存在！`,
			type: 'warning',
		});
		drugInputRef.value.focus();
		return;
	}
	if (state.detailTableData.length > 5) {
		ElMessage.error('药品不能超过5种！');
		drugInputRef.value.focus();
		return false;
	}
	state.currentDrug = { ...selectedDrug };
	state.currentDrug.singleDose = selectedDrug.dosageValue;
	state.currentDrug.singleDoseUnit = selectedDrug.dosageUnit;
	state.currentDrug.quantity = 1;
	state.currentDrug.insertSort = state.detailTableData.length + 1;
	state.currentDrug.id = 0;
	if (checkDrugPermission(state.currentDrug)) {
		state.detailTableData.splice(state.currentDrug.prescriptionIndex, 0, state.currentDrug);
		//state.detailTableData.push(_selectedDrug);
		state.detailSelectData = [];
		nextTick(() => {
			dayInputRef.value.focus();
		});
	}
	// const _selectedDrug = { ...selectedDrug };
	// _selectedDrug.singleDose = selectedDrug.dosageValue;
	// _selectedDrug.singleDoseUnit = selectedDrug.dosageUnit;
	// _selectedDrug.quantity = 1;
	// _selectedDrug.insertSort = state.detailTableData.length + 1;
	// _selectedDrug.id = 0;
	// if (checkDrugPermission(_selectedDrug)) {
	// 	state.detailTableData.splice(_selectedDrug.prescriptionIndex, 0, _selectedDrug);
	// 	//state.detailTableData.push(_selectedDrug);
	// 	state.detailSelectData = [];
	// 	nextTick(() => {
	// 		dayInputRef.value.focus();
	// 	});
	// }
};


// 页面加载时
onMounted(async () => {
	console.log('onMounted', state.currentPrescription);
	initData(); // 获取频次途径
});

const queryMain = async () => {
	queryPrescriptionList();
};

// 删除
const delPrescriptionMain = () => {
	if (state.currentPrescription.id == null || state.currentPrescription.id == 0) {
		addNew();
		return;
	}
	state.loading.mainLoading = true;
	ElMessageBox.confirm(`确定要删除吗?`, '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			await prescriptionApi.delete({ id: state.currentPrescription.id });
			queryMain();
			addNew();
			ElMessage.success('删除成功');

			state.loading.mainLoading = false;
		})
		.catch(() => {
			state.loading.mainLoading = false;
		});
};

// 组套
const detailTableRef = ref<InstanceType<typeof ElTable>>();
// 组套
const createGroup = () => {
	createPrescriptionGroup();
	detailTableRef.value?.clearSelection();
};
//选择模板后
const onSelectTemplate = (data: any) => {
	addNew
	state.detailTableData = data;
}
</script>
<style lang="scss" scoped>
// .prescriptionApplication-container {
// 	flex: 1;
// 	/* 表单容器占据剩余空间 */
// 	overflow-y: auto;
// 	/* 增加垂直滚动条 */
// 	padding: 0px;
// 	/* 可选：添加内边距 */

// }

// .foot {
// 	position: sticky;
// 	bottom: 0;
// 	background-color: white;
// 	/* 背景色防止内容穿透 */
// 	z-index: 1000;
// 	height: 50px;
// 	/* 确保底部内容在最上层 */
// }

.button-container {
	display: flex;
	justify-content: flex-end;
	padding: 0px;
	/* 可选：添加一些内边距 */
}

:deep(.el-input-group__append) {
	padding: 0px 5px;
}

:deep(.el-input-group__prepend) {
	width: 100px;
}

.list-wrapper {
	margin-top: 40px;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	border: 1px solid #f0f0f0;
	border-radius: 4px;
	overflow: auto;
}

.error {
	color: #f56c6c;
	font-size: 12px;
}
</style>

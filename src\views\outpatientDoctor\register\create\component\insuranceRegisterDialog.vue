<template>
	<div class="charge-container">
		<el-dialog v-model="state.isShowDialog" :width="850" height="100%" draggable="" :close-on-click-modal="false"
			@close="closeDialog">
			<template #header>
				<div style="color: #fff">
					<!--<el-icon size="16" style="margin-right: 3px; display: inline; vertical-align: middle"> <ele-Edit /> </el-icon>-->
					<span>{{ props.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules"
				v-loading="state.loading">
				<el-row :gutter="15">
					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="医疗统筹类别" prop="p_yltclb">
							<el-select v-model="state.ruleForm.p_yltclb" filterable remote reserve-keyword
								placeholder="医疗统筹类别" remote-show-suffix>
								<el-option label="住院" value="'1'" />
								<el-option label="家庭病床" value="'2'" />
								<el-option label="急诊转住院" value="'3'" />
								<el-option label="门诊统筹" value="'4'" />
								<el-option label="意外伤害" value="'5'" />
								<el-option label="普通门诊" value="6" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16" class="mb10">
						<el-form-item label="认证方式" prop="p_sfrzfs">
							<el-radio-group v-model="state.ruleForm.p_sfrzfs">
								<el-radio :label="'03'">有卡</el-radio>
								<el-radio :label="'00'">无卡</el-radio>
								<el-radio :label="'01'">电子凭证</el-radio>
								<el-radio :label="'05'">刷脸</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="险种标志" prop="p_xzbz">
							<el-select v-model="state.ruleForm.p_xzbz" filterable remote reserve-keyword
								placeholder="请选择险种标志" remote-show-suffix>
								<el-option label="医疗" value="C" />
								<el-option label="工伤" value="D" />
								<el-option label="生育" value="E" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb10">
						<el-form-item label="电子凭证" prop="p_ewm">
							<el-input v-model="state.ruleForm.p_ewm" disabled />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="4" :md="4" :lg="4" :xl="4" class="mb10">

						<el-button type="success" @click="getPatientInfo()">获取医保患者信息</el-button>

					</el-col>
				</el-row>

				<el-divider style="margin: 5px" />
				<el-tabs v-model="state.activeName" class="demo-tabs">
					<el-tab-pane label="医保信息" name="first"> <!-- 基础信息 -->
						<el-row :gutter="15">
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="姓名" prop="xm">
									<el-input v-model="state.ruleForm.xm" disabled>
										<template #append>{{ getLabelByDict('XB', state.ruleForm.xb) }}</template>
									</el-input>

								</el-form-item>
							</el-col>

							<!-- <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="出生日期" prop="csrq">
							<el-input v-model="state.ruleForm.csrq" disabled />
						</el-form-item>
					</el-col> -->
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="人群类别" prop="rqlb">
									<!-- A：职工，B：居民，其他具体值调用数据字典接口获取，代码编号：RQLB -->
									<el-input :value="getLabelByDict('RQLB', state.ruleForm.rqlb)" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="账户余额" prop="ye">
									<el-input v-model.number="state.ruleForm.ye" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="个人编号" prop="grbh">
									<el-input v-model="state.ruleForm.grbh" disabled />
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="社保保障号码" prop="shbzhm">
									<el-input v-model="state.ruleForm.shbzhm" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="社保机构编号" prop="sbjgbh">
									<el-input v-model="state.ruleForm.sbjgbh" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16" class="mb10">
								<el-form-item label="单位名称" prop="dwmc">
									<el-input v-model="state.ruleForm.dwmc" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="参保人员类别" prop="cbrylb">
									<el-input v-model="state.ruleForm.cbrylb" disabled />
								</el-form-item>
							</el-col>
							<!-- <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="待遇人员类别" prop="dyrylb">
							<el-input v-model="state.ruleForm.dyrylb" disabled />
						</el-form-item>
					</el-col> -->

						</el-row>



						<!-- 医保信息 -->
						<el-row :gutter="15">
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="医疗人员类别" prop="ylrylb">
									<el-input v-model="state.ruleForm.ylrylb" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="转出医院名称" prop="zcyymc">
									<el-input v-model="state.ruleForm.zcyymc" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="异地人标识" prop="ydbz">
									<el-input :value="getLabelByDict('YDBZ', state.ruleForm.ydbz)" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16" class="mb10">
								<el-form-item label="参保机构名称" prop="cbjgmc">
									<el-input v-model="state.ruleForm.cbjgmc" disabled />
								</el-form-item>
							</el-col>

							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="15天内的住院记录" prop="zhzybz">
									<el-select v-model="state.ruleForm.zhzybz" disabled reserve-keyword>
										<el-option label="无" value="0" />
										<el-option label="有" value="1" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="贫困人口标志" prop="pkrkbz">
									<el-input :value="getLabelByDict('PKRK', state.ruleForm.pkrkbz)" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="救助人员类别" prop="jzrylb">
									<el-input :value="getLabelByDict('jzrylb', state.ruleForm.pkrkbz)" disabled />
								</el-form-item>
							</el-col>
							<!-- <el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
						<el-form-item label="是否门诊统筹" prop="ptmzjbs">
							<el-input v-model="state.ruleForm.ptmzjbs" disabled />
						</el-form-item>
					</el-col> -->

							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="医疗政策标识" prop="p_ylzcbs">

									<el-select v-model="state.ruleForm.p_ylzcbs" filterable remote reserve-keyword
										remote-show-suffix>

									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="门诊急诊转诊标识" prop="p_mzjzzzbz">
									<el-select v-model="state.ruleForm.p_mzjzzzbz" filterable>
										<el-option label="急诊" value="1" />
										<el-option label="转诊" value="2" />
										<el-option label="转诊合并急诊" value="3" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="外伤标识" prop="p_wsbz">
									<el-select v-model="state.ruleForm.p_wsbz" filterable remote reserve-keyword
										remote-show-suffix>
										<el-option label="否" value="'0'" />
										<el-option label="是" value="'1'" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="涉及第三方标识" prop="p_sjdsfbz">
									<el-select v-model="state.ruleForm.p_sjdsfbz" filterable remote reserve-keyword
										remote-show-suffix>
										<el-option label="否" value="'0'" />
										<el-option label="是" value="'1'" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="外地就医类别" prop="p_jylb">
									<el-select v-model="state.ruleForm.p_jylb" filterable remote reserve-keyword
										remote-show-suffix>
										<el-option label="本地定点就医" value="'01'" />
										<el-option label="异地治疗" value="'10'" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="异地就医医院编码" prop="ydjyyybm">
									<el-input v-model="state.ruleForm.ydjyyybm" />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="16" :md="16" :lg="16" :xl="16" class="mb10">
								<el-form-item label="诊断" prop="diagnosticCode">
									<el-select filterable v-model="state.ruleForm.diagnosticCode" remote reserve-keyword
										:remote-method="icd10RemoteMethod" :loading="state.icd10Loading"
										@change="(val: any) => diagnosticCodeChange(val, 'diagnosticName')"
										placeholder="请选择诊断">
										<el-option v-if="state.icd10Data.length == 0"
											:value="state.ruleForm.diagnosticCode ?? ''"
											:label="state.ruleForm.diagnosticName ?? ''"> </el-option>
										<el-option v-else v-for="item in state.icd10Data" :key="item.code"
											:label="item.name" :value="item.code" />
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>

						<el-divider style="margin: 5px" />

						<!-- 其他说明 -->
						<el-row :gutter="15">
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="门诊定点标志" prop="mzddbz">
									<el-input v-model="state.ruleForm.mzddbz" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
								<el-form-item label="门诊定点说明" prop="mzddsm">
									<el-input type="textarea" v-model="state.ruleForm.mzddsm" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="8" :md="8" :lg="8" :xl="8" class="mb10">
								<el-form-item label="灰白名单标志" prop="zfbz">
									<!-- 支付标志（灰名单标志） 0 :灰名单，1:白名单 -->
									<el-input :value="getLabelByDict('ZFBZ', state.ruleForm.zfbz)" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
								<el-form-item label="灰白名单原因" prop="zfsm">
									<el-input type="textarea" v-model="state.ruleForm.zfsm" disabled />
								</el-form-item>
							</el-col>
							<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb10">
								<el-form-item label="疾病说明" prop="jbsm">
									<el-input type="textarea" v-model="state.ruleForm.jbsm" />
								</el-form-item>
							</el-col>
						</el-row></el-tab-pane>
					<el-tab-pane label="费用凭单" name="second">
						<el-card shadow="hover" style="height: 100%" :body-style="{ padding: '0px' }">

							<el-table :data="state.items" style="width: 100%">
								<!-- 必填字段用 * 标注 -->
								<el-table-column prop="itemCode" label="*医院项目编码" width="150" />
								<el-table-column prop="itemName" label="医院项目名称" width="150" />
								<el-table-column prop="price" label="*单价" width="120" align="right">

								</el-table-column>
								<el-table-column prop="quantity" label="*数量" width="100" align="right" />
								<el-table-column prop="amount" label="*总金额" width="120" align="right">
									<template #default="{ row }">
										{{ (row.dj * row.sl).toFixed(2) }}
									</template>
								</el-table-column>
								<el-table-column prop="executeDeptId" label="*执行科室编码" width="150" />
								<el-table-column prop="executeDeptName" label="*开单科室编码" width="150" />
								<!-- <el-table-column prop="sxzfbl" label="*自付比例" width="120" align="right">
									<template #default="{ row }">
										{{ (row.sxzfbl * 100).toFixed(2) }}%
									</template>
								</el-table-column> -->
								<el-table-column prop="executeTime" label="*费用发生时间" width="180" />
								<el-table-column prop="spec" label="规格" width="120" />
								<el-table-column prop="medicationDays" label="用药天数" width="100" align="right" />
								<!-- <el-table-column prop="bzsl" label="大包装的小包装数量" width="180" align="right" />
								<el-table-column prop="sm" label="说明" width="150" />
								<el-table-column prop="yysm" label="用药说明" width="200" /> -->
								<el-table-column prop="billingDetailId" label="医嘱流水号" width="150" />
								<!-- <el-table-column prop="sfryxm" labsel="收费人员姓名" width="150" />
								<el-table-column prop="gytj" label="给药途径" width="120"> 

								</el-table-column>-->
								<el-table-column prop="singleDose" label="单次用量" width="100" align="right" />
								<!-- <el-table-column prop="yypc" label="用药频次" width="120">

								</el-table-column> -->
								<!-- <el-table-column prop="wpbz" label="外配标志" width="100">
									<template #default="{ row }">
										{{ row.wpbz === '1' ? '是' : '否' }}
									</template>
								</el-table-column> -->
								<el-table-column prop="executeDoctorId" label="执行医师编码" width="150" />
								<el-table-column prop="billingDoctorId" label="*开单医师编码" width="150" />
								<!-- <el-table-column prop="zsm" label="追溯码" width="200">
									<template #default="{ row }">
										<div v-if="row.zsm">
											<el-tag v-for="(code, index) in row.zsm.split(',')" :key="index"
												size="small" style="margin-right: 5px;">
												{{ code }}
											</el-tag>
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="clbz" label="拆零标志" width="100">
									<template #default="{ row }">
										{{ row.clbz === '1' ? '是' : '否' }}
									</template>
								</el-table-column> -->
							</el-table>
							<!-- <el-table :data="state.items" style="width: 100%">
							 
								<el-table-column prop="yyxmbm" label="*医院项目编码" width="150" />
								<el-table-column prop="yyxmmc" label="医院项目名称" width="150" />
								<el-table-column prop="dj" label="*单价" width="120" align="right">
									<template #default="{ row }">
										{{ row.dj.toFixed(2) }}
									</template>
								</el-table-column>
								<el-table-column prop="sl" label="*数量" width="100" align="right" />
								<el-table-column prop="zje" label="*总金额" width="120" align="right">
									<template #default="{ row }">
										{{ (row.dj * row.sl).toFixed(2) }}
									</template>
								</el-table-column>
								<el-table-column prop="zxksbm" label="*执行科室编码" width="150" />
								<el-table-column prop="kdksbm" label="*开单科室编码" width="150" />
								<el-table-column prop="sxzfbl" label="*自付比例" width="120" align="right">
									<template #default="{ row }">
										{{ (row.sxzfbl * 100).toFixed(2) }}%
									</template>
								</el-table-column>
								<el-table-column prop="fyfssj" label="*费用发生时间" width="180" />
								<el-table-column prop="gg" label="规格" width="120" />
								<el-table-column prop="yyts" label="用药天数" width="100" align="right" />
								<el-table-column prop="bzsl" label="大包装的小包装数量" width="180" align="right" />
								<el-table-column prop="sm" label="说明" width="150" />
								<el-table-column prop="yysm" label="用药说明" width="200" />
								<el-table-column prop="yzlsh" label="医嘱流水号" width="150" />
								<el-table-column prop="sfryxm" label="收费人员姓名" width="150" />
								<el-table-column prop="gytj" label="给药途径" width="120">

								</el-table-column>
								<el-table-column prop="dcyl" label="单次用量" width="100" align="right" />
								<el-table-column prop="yypc" label="用药频次" width="120">

								</el-table-column>
								<el-table-column prop="wpbz" label="外配标志" width="100">
									<template #default="{ row }">
										{{ row.wpbz === '1' ? '是' : '否' }}
									</template>
								</el-table-column>
								<el-table-column prop="zxysbm" label="执行医师编码" width="150" />
								<el-table-column prop="kdysbm" label="*开单医师编码" width="150" />
								<el-table-column prop="zsm" label="追溯码" width="200">
									<template #default="{ row }">
										<div v-if="row.zsm">
											<el-tag v-for="(code, index) in row.zsm.split(',')" :key="index"
												size="small" style="margin-right: 5px;">
												{{ code }}
											</el-tag>
										</div>
									</template>
								</el-table-column>
								<el-table-column prop="clbz" label="拆零标志" width="100">
									<template #default="{ row }">
										{{ row.clbz === '1' ? '是' : '否' }}
									</template>
								</el-table-column>
							</el-table> -->
						</el-card>

					</el-tab-pane>
				</el-tabs>

			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="cancel" :disabled="state.loading">取 消</el-button>
					<el-button type="primary" @click="submit" :loading="state.loading">确 定</el-button>
				</span>
			</template>
		</el-dialog>
		<InsurancePreSettlementDialog ref="insurancePreSettlementRef" :title="'医保预结算'"
			@settlementFinish="settlementFinish" />
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select),
:deep(.el-input-number) {
	width: 100%;
}
</style>
<script lang="ts" setup name="charge">
import { ref, onMounted, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormRules } from 'element-plus';

import { useBasicInfoApi } from '/@/api/shared/basicInfo';
import { useBaseApi } from '/@/api/base';
import InsurancePreSettlementDialog from './insurancePreSettlementDialog.vue';
import {
	InsuranceRegister,
	SettleMzPreRequest,
	SettleMzPreBcxm,
	SettleMzPreJbbm,
	SettleMzPreFypd
} from '/@/models/insurance';
import { forEach } from 'lodash-es';
// API服务
const insurancePatientApi = useBaseApi("insurancePatient");
const basicInfoApi = useBasicInfoApi();

//父级传递来的参数
var props = defineProps({
	title: {
		type: String,
		default: '',
	},
});
const insurancePreSettlementRef = ref<InstanceType<typeof InsurancePreSettlementDialog>>();
//父级传递来的函数，用于回调
const emit = defineEmits(['registerCancel', 'registerFinish']);
const ruleFormRef = ref();

const state = reactive({
	loading: false,
	itemsLoading: false,
	items: [] as Array<any>,
	activeName: 'first',
	icd10Loading: false,
	icd10Data: [] as Array<any>,
	isShowDialog: false,
	paymentMethodData: [] as Array<any>,
	ruleForm: {
		p_sfrzfs: "00", // 默认有卡
	} as any,
	register: {} as InsuranceRegister, //挂号信息
});
//自行添加其他规则
const rules = ref<FormRules>({
	paymentMethod: [{ required: true, message: '请选择支付方式！', trigger: 'change' }],
	diagnosticCode: [{ required: true, message: '请选择诊断！', trigger: 'change' }],
});

// 打开弹窗
const openDialog = async (register: InsuranceRegister) => {



	// 重置表单
	ruleFormRef.value?.resetFields();

	state.ruleForm.p_yltclb = register.insurance.medicalPoolingCategory;
	state.ruleForm.p_xzbz = register.insurance.medicalInsuranceFlag;
	state.register = register;
	state.ruleForm.idCardNo = register.idCardNo;
	state.ruleForm.xm = register.name;
	state.ruleForm.diagnosticCode = register.diagnosticCode ?? '';
	state.ruleForm.diagnosticName = register.diagnosticName ?? '';


	// 只有在第一次打开对话框时才加载数据
	if (state.paymentMethodData.length === 0) {
		let res = await basicInfoApi.getPayMethods({});
		state.paymentMethodData = res.data.result ?? [];
	}
	state.isShowDialog = true;
	console.log('医保患者信息登记 register=', register, 'state.ruleForm=', state.ruleForm);
	// 获取卡余额
	getPatientInfo();
	// 处理费用
	convertChargeItems(register.chargeList ?? []);
};
const convertChargeItems = (chargeItems: any[]) => {
	state.items = chargeItems;

};

// icd10
const icd10RemoteMethod = async (query: string) => {
	const res = await basicInfoApi.getIcd10s({ keyword: query });
	state.icd10Data = (res.data.result ?? []).map((item: any) => ({ code: item.code, name: item.name }));
};
//选择诊断后
const diagnosticCodeChange = (value: any, name: any) => {
	var obj = state.icd10Data.find((item) => {
		//数据源
		return item.code === value;
	});
	state.ruleForm.diagName = obj?.name ?? value;
};

// 关闭弹窗
const closeDialog = () => {

	state.isShowDialog = false;
};

// 取消
const cancel = () => {
	emit('registerCancel', state.ruleForm);
	closeDialog();
};

// 字典对照
const dictMap = {
	XB: { 1: '男', 2: '女', 9: '不确定' },
	RQLB: { 'A': '职工', 'B': '居民' },
	YDBZ: {
		0: '非异地', 1: '异地'
	},
	PKRK: { 0: '非贫困人口', 1: '贫困人口' },
	ZFBZ: { 0: '灰名单', 1: '白名单' },
	// 可扩展其他字段...
} as any;
const getLabelByDict = (dictType: any, value: string | number) => {
	const dict = dictMap[dictType];
	return dict ? dict[value] || '' : '';
};
// 远程获取
//const dictMap = ref<Record<string, Record<string, string>>>({}); // 存储字典数据

// 获取数据字典
// const loadDict = async () => {
// 	const res = await basicInfoApi.getDicts({ codes: ['XB', 'RQLB'] }); // 接口示例
// 	res.data.result.forEach((item: { code: string; items: Array<{ value: string; label: string }> }) => {
// 		dictMap.value[item.code] = item.items.reduce((acc, cur) => {
// 			acc[cur.value] = cur.label;
// 			return acc;
// 		}, {} as Record<string, string>);
// 	});
// };

const settlementFinish = () => {

};
// 提交

const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {

			let params = buildSettlePreParams();
			console.log('提交医保患者信息登记 params=', params);
			//跳转到预结算
			insurancePreSettlementRef.value?.openDialog(state.register, params);
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: 'error',
			});
		}
	});
};
const buildSettlePreParams = () => {
	//预结算参数
	let params: SettleMzPreRequest = {
		p_yltclb: state.ruleForm.p_yltclb,
		p_grbh: state.ruleForm.grbh,
		p_xm: state.ruleForm.xm,
		p_xb: state.ruleForm.xb,
		p_blh: state.register.outpatientNo,
		//p_fyrq: '',
		p_ysbm: '',// 医生编码
		p_xzbz: state.ruleForm.xzbz,
		p_jylb: state.ruleForm.jylb,//外地就医类别
		p_jyyybm: state.ruleForm.ydjyyybm,//外地就医类别
		p_kh: '',
		p_ptmzskbz: '',
		p_jbbm: state.ruleForm.diagnosticCode,
		p_jbsm: state.ruleForm.jbsm,
		p_ewm: state.ruleForm.p_ewm, // 电子凭证
		p_authno: state.ruleForm.p_ewm, //  刷脸授权码（医保三类终端刷脸返回的 authNo） 
		p_ylzcbs: state.ruleForm.p_ylzcbs, // 医疗政策标识
		p_zyzd: state.ruleForm.diagnosticCode,//主要诊断（跨省异地门诊大病结算必传）
		p_sfrzfs: state.ruleForm.p_sfrzfs, // 收费认证方式
		p_mzjzzzbz: state.ruleForm.p_mzjzzzbz, // 门诊急诊转诊标识
		p_wsbz: state.ruleForm.p_wsbz, // 外伤标识
		p_sjdsfbz: state.ruleForm.p_sjdsfbz, // 涉及第三方标识
		p_yltclbmx: '',
		p_ksbm: String(state.register.deptId), // 开单科室编码
	}
	//
	params.p_kh = '';
	params.p_ptmzskbz = '';
	params.p_yltclb = state.ruleForm.p_yltclb;
	if (params.p_jylb === '10')// 异地治疗
	{

	}
	// 补充项目信息（通过 get_bcxm 获取需录入项）
	params.p_bcxm_ds = [{ bcxmbh: '', bcxmz: '' } as SettleMzPreBcxm];
	// 多诊断
	params.p_jbbm_ds = [] as SettleMzPreJbbm[];
	for (let i = 0; i < state.register.diagList.length; i++) {
		const item = state.register.diagList[i];
		params.p_jbbm_ds.push({
			dzdjbbm: item.diagCode,
			maindiag_flag: item.isMainDiag, // 主诊断标志
			diag_type: item.diagType, // 诊断类型
			diag_srt_no: item.orderNo, // 诊断顺序号
			diag_dept: item.deptId, // 诊断科室
			diag_dr_code: item.doctorId, // 诊断医生编码
			diag_dr_name: item.doctorName, // 诊断医生姓名
			diag_time: item.diagTime, // 诊断日期
		} as SettleMzPreJbbm);
	}
	//费用
	params.p_fypd_ds = [] as SettleMzPreFypd[];
	// for (let i = 0; i < state.register.chargeList.length; i++) {
	// 	const item = state.register.chargeList[i];
	// 	params.p_fypd_ds.push({
	// 		// 根据实际需要映射字段，以下为示例字段
	// 		yyxmbm: item.itemCode,
	// 		yyxmmc: item.itemName,
	// 		dj: item.price,
	// 		zje: item.amount,
	// 		sl: item.quantity,
	// 		zxksbm: item.executingDeptId,
	// 		kdksbm: item.billingDeptId,
	// 		gg: item.spec,
	// 		bzsl: 0, // 大包装的小包装数量
	// 		yyts: item.medicationDays,
	// 		yysm: item.remark, // 用药说明
	// 		yzlsh: item.billingDetailId,// 医嘱流水号
	// 		sfryxm: '',
	// 		gytj: '',
	// 		yypc: '',// 用药频次
	// 		dcyl: item.singleDose, // 单次用量
	// 		zxysbm: item.executeDoctorId, // 执行医师编码
	// 		kdysbm: item.billingDoctorId, // 开单医师编码
	// 		clbz: '0',
	// 		sxzfbl: 0, // 自付比例
	// 		// ... 其他需要的字段
	// 	} as SettleMzPreFypd);
	// }
	return params;
};
///
const getPatientInfo = async () => {

	console.log('获取医保患者信息 state.ruleForm.acquisitionMethod=', state.register.acquisitionMethod);
	if (state.register.acquisitionMethod === '03') {
		// 有卡
		state.ruleForm.sbjgmc = state.register.insurance.medicalInstitutionCode;
		state.ruleForm.sbjgbh = state.register.insurance.medicalInsuranceCardNumber;
	}
	else if (state.register.acquisitionMethod === '01') {
		// 电子凭证
		state.ruleForm.sbjgmc = state.register.insurance.medicalInstitutionCode;
		state.ruleForm.sbjgbh = state.register.insurance.medicalInsuranceCardNumber;
	} else if (state.register.acquisitionMethod === '02') {
		// 刷脸
		state.ruleForm.sbjgmc = '';
		state.ruleForm.sbjgbh = '';
	} else // if (state.register.acquisitionMethod === '00') {
	{	// 无卡
		state.loading = true;
		await insurancePatientApi.post("QueryBasicInfo", {
			p_grbh: state.ruleForm.idCardNo,
			p_xzbz: state.ruleForm.p_xzbz,
			p_xm: state.register.name,
			p_yltclb: state.ruleForm.p_yltclb
		}).then((res) => {
			Object.assign(state.ruleForm, res.data.result);
			console.log(res.data.result, state.ruleForm);
		}).finally(() => {
			// 关闭加载状态
			state.loading = false;
		});

	}

};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>

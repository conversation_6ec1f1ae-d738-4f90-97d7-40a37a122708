/* tslint:disable */
/* eslint-disable */
/**
 * OutDoctor
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { RegStatusEnum } from './reg-status-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 更新挂号信息输入参数
 *
 * @export
 * @interface UpdateMzRegisterInput
 */
export interface UpdateMzRegisterInput {

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateMzRegisterInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateMzRegisterInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof UpdateMzRegisterInput
     */
    isDelete?: boolean;

    /**
     * 创建者部门Id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    createOrgId?: number | null;

    /**
     * 创建者部门名称
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    createOrgName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    tenantId?: number | null;

    /**
     * 就诊号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    visitNo?: string | null;

    /**
     * 就诊次数
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    visitNum?: number | null;

    /**
     * 时间段id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    timePeriodId?: number | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    patientId?: number | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    wubiCode?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof UpdateMzRegisterInput
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof UpdateMzRegisterInput
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    idCardNum?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    occupation?: string | null;

    /**
     * 保险号码
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    insuranceNum?: string | null;

    /**
     * 保险类型
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    insuranceType?: string | null;

    /**
     * 合同单位id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    contractUnitId?: number | null;

    /**
     * 就诊类型 初诊|复诊
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    visitType?: number | null;

    /**
     * @type {RegStatusEnum}
     * @memberof UpdateMzRegisterInput
     */
    status?: RegStatusEnum;

    /**
     * 症状
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    symptom?: string | null;

    /**
     * 挂号费
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    registrationFee?: number | null;

    /**
     * 诊疗费
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    consultationFee?: number | null;

    /**
     * 其他费用
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    otherFee?: number | null;

    /**
     * 实收费用
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    actualChargeFee?: number | null;

    /**
     * 退号时间
     *
     * @type {Date}
     * @memberof UpdateMzRegisterInput
     */
    refundNumTime?: Date | null;

    /**
     * 退号人id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    refundNumId?: number | null;

    /**
     * 就诊卡id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    cardId?: number | null;

    /**
     * 收费主表id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    chargeMainId?: number | null;

    /**
     * 预约流水号
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    appSerialNum?: number | null;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    remark?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    nation?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    nativePlaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    residenceProvince?: number | null;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    residenceCity?: number | null;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    residenceCounty?: number | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    contactName?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    contactPhone?: string | null;

    /**
     * 医保卡余额
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    medInsCardBalance?: number | null;

    /**
     * 个人账户信息
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    personalAccountInfo?: string | null;

    /**
     * 医保个人编号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    medInsPersonalNum?: string | null;

    /**
     * 医保类型
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    medInsType?: number | null;

    /**
     * 医保统筹区号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    medInsAreaCode?: string | null;

    /**
     * 医保就诊编号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    medInsRegNum?: string | null;

    /**
     * 医保支付方式 0:门诊统筹 1：个人支付
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    medInsPayType?: string | null;

    /**
     * 结算病种id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    settleDiseaseTypeId?: number | null;

    /**
     * 首次就诊科室id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    firstDeptId?: number | null;

    /**
     * 首次就诊医生id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    firstDoctorId?: number | null;

    /**
     * @type {YesNoEnum}
     * @memberof UpdateMzRegisterInput
     */
    isNoCard?: YesNoEnum;

    /**
     * 险种标志
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    insuranceSign?: string | null;

    /**
     * 门诊号 第一次就诊的流水号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    outpatientNo?: string | null;

    /**
     * 就医类别
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    medTreatCategory?: string | null;

    /**
     * 发病日期
     *
     * @type {Date}
     * @memberof UpdateMzRegisterInput
     */
    onsetDate?: Date | null;

    /**
     * 诊断编码
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    diagnosticCode?: string | null;

    /**
     * 诊断名称
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    diagnosticName?: string | null;

    /**
     * 病种代码
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    diseaseTypeCode?: string | null;

    /**
     * 病种名称
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    diseaseTypeName?: string | null;

    /**
     * 门诊急诊转诊标志
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    outEmeReferralSign?: string | null;

    /**
     * 外伤标志
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    traumaSign?: string | null;

    /**
     * 涉及第三方标志
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    thirdPartySign?: string | null;

    /**
     * 号别id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    regCategoryId: number;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    patientName: string;

    /**
     * @type {GenderEnum}
     * @memberof UpdateMzRegisterInput
     */
    sex: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    age: number;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    ageUnit: string;

    /**
     * 费别
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    feeId: number;

    /**
     * 科室
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    deptId: number;

    /**
     * 医生
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    doctorId: number;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof UpdateMzRegisterInput
     */
    cardNo?: string | null;

    /**
     * 总金额
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    totalAmount?: number | null;

    /**
     * 支付方式
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    paymentMethod?: number | null;

    /**
     * 个人支付
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    personalPayment?: number | null;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateMzRegisterInput
     */
    id: number;
}

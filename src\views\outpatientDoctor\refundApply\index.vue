﻿<script lang="ts" setup name="refundApply">
import { h, ref, reactive, onMounted, triggerRef } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useRefundApplyApi } from '/@/api/outpatientDoctor/refundApply';
import editDialog from '/@/views/outpatientDoctor/refundApply/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import ImportData from "/@/components/table/importData.vue";
import { useRouter } from 'vue-router'
import { compact } from "lodash-es";
import { formatNumber } from '/@/utils/formatObject';
import { formatOnlyDate } from '/@/utils/formatTime';
import { useOutpatientChargeApi } from '../../../api/outpatientDoctor/charge';
import { ElDivider } from 'element-plus'
import { useRefundAuditApi } from '/@/api/outpatientDoctor/refundAudit';

import { useVisitApi } from '/@/api/outpatientDoctor/visit';
import Dateline from '/@/views/outpatientDoctor/refundApply/component/Dateline.vue';
const router = useRouter() // 路由对象
const visitApi = useVisitApi();
const spacer = h(ElDivider, { direction: 'vertical' })
const outpatientChargeApi = useOutpatientChargeApi();
const refundApplyApi = useRefundApplyApi();
const refundAuditApi = useRefundAuditApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
  audit: {
    dialogVisible: false,
    auditStatus: 0,
    auditReason: ''

  } as any,

  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符

  },
  tableData: [],
});


const create = () => {
  router.push({ path: '/outpatientDoctor/refundapply/create', state: { type: 'add' } })
}
const audit = (auditStatus: number) => {
  state.tableLoading = true;
  refundAuditApi.add({
    applyId: state.audit.currentRow.id,
    auditStatus: auditStatus,
    auditReason: state.audit.auditReason
  }).then(res => {
    state.tableLoading = false;
    state.audit.dialogVisible = false; // 关闭对话框
    handleQuery();
    ElMessage.success("操作成功");
  }).catch(() => {
    state.tableLoading = false;
  });
};

// 页面加载时
onMounted(async () => {
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await refundApplyApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};
const getAgeUnit = ((ageUnit: any) => {
  if (ageUnit === "0")
    return '岁'
  else if (ageUnit === "1")
    return '月'
  else if (ageUnit === "2")
    return '天'
  else return ''

});
// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delRefundApply = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await refundApplyApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => { });
};
const getbillingTypeName = (row: any) => {
  switch (row.billingType) {
    case "Prescription":
      return '处方'
    case "prescription":
      return '处方'
    case "Dispose":
      return '处置'
    case "Examination":
      return '检查'
    case "LabTest":
      return '检验'
    default:
      return ''
  }

}

// 批量删除
const batchDelRefundApply = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await refundApplyApi.batchDelete(state.selectData.map(u => ({ id: u.id }))).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => { });
};

// 导出数据
const exportRefundApplyCommand = async (command: string) => {
  try {
    state.exportLoading = true;
    if (command === 'select') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map(u => u.id) });
      await refundApplyApi.exportData(params).then(res => downloadStreamFile(res));
    } else if (command === 'current') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams);
      await refundApplyApi.exportData(params).then(res => downloadStreamFile(res));
    } else if (command === 'all') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
      await refundApplyApi.exportData(params).then(res => downloadStreamFile(res));
    }
  } finally {
    state.exportLoading = false;
  }
}
// 新增：控制展开行的响应式变量
const expandedRows = ref<number[]>([]);

// 处理展开/收起事件
const handleExpandChange = async (row: any, expanded: boolean) => {
  try {
    if (expanded) {
      // 设置展开状态
      // expandedRows.value = [row.id];

      // 如果还没有加载过数据或数据为空，则加载数据
      if (!row.details || row.details.length === 0) {
        // 显示加载状态
        row.loading = true;

        // 调用API获取子表数据
        const res = await outpatientChargeApi.getDetail(row.chargeId);
        // 将获取的数据赋值给子表
        row.details = res.data?.result || [];
        // 查询就诊信息 
        await visitApi.getVisit(row.visitNo).then(res => {
          row.visitInfo = res.data.result ?? {};
        })

        // 审核记录
        await refundAuditApi.list({ applyId: row.id }).then(res => {

          row.auditData = res.data.result ?? [];
        })
      }
    } else {
      // 收起时清空展开状态
      //   expandedRows.value = [];
    }
  } catch (error) {
    console.error('加载子表数据失败:', error);
    row.details = [];
  } finally {
    // 无论成功失败都关闭加载状态
    row.loading = false;
  }
};
// 打开审核对话框
const openAuditDialog = (row: any) => {
  state.audit.currentRow = row;
  state.audit.dialogVisible = true;
  state.audit.auditReason = ''; // 清空上次的审核原因
};
handleQuery();
</script>
<template>
  <div class="refundApply-container" v-loading="state.exportLoading">
    <el-card shadow="hover" :body-style="{ paddingBottom: '0' }">
      <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="90">
        <el-row>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item label="关键字">
              <el-input v-model="state.tableQueryParams.keyword" clearable placeholder="就诊号/卡号/患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item label="退费申请时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.applyTimeRange"
                value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期"
                :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item label="审核状态">
              <el-select v-model="state.tableQueryParams.auditStatus" clearable placeholder="请选择状态">
                <el-option label="待审核" value="0"></el-option>
                <el-option label="审核中" value="1"></el-option>
                <el-option label="驳回" value="2"></el-option>
                <el-option label="审核完成" value="3"></el-option>
                <el-option label="退费完成" value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="退费申请单号">
              <el-input v-model="state.tableQueryParams.applyNo" clearable placeholder="请输入退费申请单号" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="退费申请类型">
              <el-input v-model="state.tableQueryParams.applyType" clearable placeholder="请输入退费申请类型" />
            </el-form-item>
          </el-col> -->



          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item>
              <el-button-group style="display: flex; align-items: center;">
                <el-button type="primary" icon="ele-Search" @click="handleQuery" v-auth="'refundApply:page'"
                  v-reclick="1000"> 查询 </el-button>
                <el-button icon="ele-Refresh" @click="() => state.tableQueryParams = {}"> 重置 </el-button>
                <!-- <el-button icon="ele-ZoomIn" @click="() => state.showAdvanceQueryUI = true"
                  v-if="!state.showAdvanceQueryUI" style="margin-left:5px;"> 高级查询 </el-button>
                <el-button icon="ele-ZoomOut" @click="() => state.showAdvanceQueryUI = false"
                  v-if="state.showAdvanceQueryUI" style="margin-left:5px;"> 隐藏 </el-button> -->

                <el-button type="primary" style="margin-left:5px;" icon="ele-Plus" @click="create"
                  v-if="auth('refundApply:add')" v-auth="'refundApply:add'"> 新增 </el-button>

              </el-button-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="full-table" shadow="hover" style="margin-top: 5px">
      <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }"
        style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id"
        :expand-row-keys="expandedRows" @expand-change="handleExpandChange" @sort-change="sortChange" border>
        <el-table-column type="selection" width="40" align="center"
          v-if="auth('refundApply:batchDelete') || auth('refundApply:export')" />
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column type="expand" label="展开" width="55" align="center" fixed="left">
          <template #default="props">



            <div style="margin-left: 150px;margin-bottom: 20px;">

              <el-space :size="5" :spacer="spacer" v-if="props.row.visitInfo != null">

                <el-text class="mx-1" size="large">{{ props.row.visitInfo.patientName ?? "" }}</el-text>

                <el-text class="mx-1" size="small"><g-sys-dict v-model="props.row.visitInfo.sex"
                    code="GenderEnum" /></el-text>

                <el-text><span style="margin-right: 5px;">{{ props.row.visitInfo.age
                    }}{{ getAgeUnit(props.row.visitInfo.ageUnit) ?? '' }}</span> {{
                      formatOnlyDate(props.row.visitInfo.birthday
                        ?? '') }} </el-text>
                <el-text> {{ props.row.visitInfo.deptName }}[{{ props.row.visitInfo.doctorName }} ]</el-text>
                <!-- <el-text>门诊号：{{ props.row.visitInfo.outpatientNo }} </el-text>
                <el-text>就诊号：{{ props.row.visitInfo.visitNo }} </el-text> -->
                <el-text>就诊时间：{{ props.row.visitInfo.createTime }} </el-text>
                <el-text>卡号：{{ props.row.visitInfo.cardNo }} </el-text>
                <el-text> {{ props.row.visitInfo.feeName }} </el-text>

                <el-text> <el-tag type="danger">余额：{{ props.row.visitInfo.balance }}</el-tag> </el-text>
              </el-space>
              <div style="display: flex; justify-content: left; ">


                <Dateline :data="props.row.auditData || []" />
              </div>
              <el-table id="prescriptionTable" :data="props.row.details" border class="success-row"
                v-loading="props.row.loading">
                <el-table-column prop='itemName' label='明细名称' show-overflow-tooltip width="140" />

                <el-table-column prop='spec' label='规格' show-overflow-tooltip />
                <el-table-column prop='number' label='数量' show-overflow-tooltip>
                  <template #default="scope">
                    <span>{{ scope.row.quantity }} {{ scope.row.unit }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop='price' label='价格' show-overflow-tooltip align="right">
                  <template #default="scope">
                    <span>{{ formatNumber(scope.row.price) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop='amount' label='金额' show-overflow-tooltip align="right">
                  <template #default="scope">
                    <span>{{ formatNumber(scope.row.amount) }}</span>
                  </template>
                </el-table-column>

                <el-table-column prop='manufacturer' label='生产厂家' show-overflow-tooltip width="140" />
                <el-table-column prop='medicineCode' label='国家医保编码' show-overflow-tooltip width="140" />
                <el-table-column prop='itemCode' label='明细编码' show-overflow-tooltip />

                <el-table-column prop='chargeCategoryCode' label='收费类别' show-overflow-tooltip width="140" />
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop='applyNo' label='退费申请单号' show-overflow-tooltip>
          <!-- <template #default="scope">
            <el-link type="primary" style="font-size: 12px" @click="editDialogRef.openDialog(scope.row, '门诊退费申请')">{{
              scope.row.applyNo
              }}</el-link>
          </template> -->
        </el-table-column>
        <el-table-column prop='billingType' label='退费申请类型' show-overflow-tooltip>
          <template #default="scope">

            {{ getbillingTypeName(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop='applyTime' label='退费申请时间' show-overflow-tooltip />
        <el-table-column prop='createOrgName' label='申请科室' show-overflow-tooltip />
        <el-table-column prop='createUserName' label='申请人' show-overflow-tooltip />
        <el-table-column prop='visitNo' label='就诊号' show-overflow-tooltip />
        <el-table-column prop='outpatientNo' label='门诊号' show-overflow-tooltip />
        <el-table-column prop='patientName' label='患者名称' show-overflow-tooltip />
        <el-table-column prop='applyReason' label='退费原因' show-overflow-tooltip />
        <el-table-column prop='auditStatus' label='状态' show-overflow-tooltip>
          <template #default="scope">

            <el-tag v-if="scope.row.auditStatus == 0" type="info">待审核</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 1" type="warning">审核中</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 2" type="danger">驳回</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 3" type="success">审核完成</el-tag>
            <el-tag v-else-if="scope.row.auditStatus == 4" type="success">退费完成</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="审核" width="140" align="center" fixed="right" show-overflow-tooltip
          v-if="auth('refundApply:update') || auth('refundApply:delete')">
          <template #default="scope">
            <el-button :disabled="!scope.row.hasAuditPermission" icon="ele-Edit" size="small" text type="primary"
              @click="openAuditDialog(scope.row)"> 审核 </el-button>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:currentPage="state.tableParams.page" v-model:page-size="state.tableParams.pageSize"
        @size-change="(val: any) => handleQuery({ pageSize: val })"
        @current-change="(val: any) => handleQuery({ page: val })" layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100, 200, 500]" :total="state.tableParams.total" size="small" background />
      <ImportData ref="importDataRef" :import="refundApplyApi.importData" :download="refundApplyApi.downloadTemplate"
        v-auth="'refundApply:import'" @refresh="handleQuery" />
      <printDialog ref="printDialogRef" :title="'打印门诊退费申请'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>

    <el-dialog v-model="state.audit.dialogVisible" v-loading="state.tableLoading" width="30%"
      @closed="state.audit.currentRow = null">
      <template #header>
        <div style="color: #fff">
          <span>审核批注</span>
        </div>
      </template>
      <el-input v-model="state.audit.auditReason" clearable placeholder="请输入审核意见" type="textarea" />

      <template #footer style="padding-top: 0px;">
        <span class="dialog-footer">

          <el-button type="success" @click="audit(1)">通过</el-button>
          <el-button type="danger" @click="audit(2)">驳回</el-button>
          <el-button @click="state.audit.dialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style scoped>
:deep(.el-input),
:deep(.el-select),
:deep(.el-input-number) {
  width: 100%;
}
</style>
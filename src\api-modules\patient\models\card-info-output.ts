/* tslint:disable */
/* eslint-disable */
/**
 * Patient
 * 患者管理
 *
 * 
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { BusinessTypeEnum } from './business-type-enum';
import { CardStatusEnum } from './card-status-enum';
import { CardTypeEnum } from './card-type-enum';
import { GenderEnum } from './gender-enum';
import { MedInsTypeEnum } from './med-ins-type-enum';
import { YesNoEnum } from './yes-no-enum';
 /**
 * 就诊卡管理输出参数
 *
 * @export
 * @interface CardInfoOutput
 */
export interface CardInfoOutput {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof CardInfoOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof CardInfoOutput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    updateUserName?: string | null;

    /**
     * 软删除
     *
     * @type {boolean}
     * @memberof CardInfoOutput
     */
    isDelete?: boolean;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    tenantId?: number | null;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    name?: string | null;

    /**
     * 英文姓名
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    englishName?: string | null;

    /**
     * 姓名拼音码
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    pinyinCode?: string | null;

    /**
     * 姓名五笔码
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    wubiCode?: string | null;

    /**
     * @type {GenderEnum}
     * @memberof CardInfoOutput
     */
    sex?: GenderEnum;

    /**
     * 年龄
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    age?: number;

    /**
     * 年龄单位
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    ageUnit?: string | null;

    /**
     * 出生日期
     *
     * @type {Date}
     * @memberof CardInfoOutput
     */
    birthday?: Date | null;

    /**
     * @type {CardTypeEnum}
     * @memberof CardInfoOutput
     */
    cardType?: CardTypeEnum;

    /**
     * 身份证号
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    idCardNum?: string | null;

    /**
     * 民族
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    nation?: string | null;

    /**
     * 电话号码
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    phone?: string | null;

    /**
     * 联系人姓名
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    contactName?: string | null;

    /**
     * 联系人关系
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    contactRelationship?: string | null;

    /**
     * 联系人地址
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    contactAddress?: string | null;

    /**
     * 联系人电话号码
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    contactPhone?: string | null;

    /**
     * 国籍
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    nationality?: string | null;

    /**
     * 职业
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    occupation?: string | null;

    /**
     * 婚姻
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    marriage?: string | null;

    /**
     * 籍贯省
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    nativePlaceProvince?: number | null;

    /**
     * 籍贯市
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    nativePlaceCity?: number | null;

    /**
     * 籍贯县
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    nativePlaceCounty?: number | null;

    /**
     * 出生地省
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    birthplaceProvince?: number | null;

    /**
     * 出生地市
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    birthplaceCity?: number | null;

    /**
     * 出生地县
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    birthplaceCounty?: number | null;

    /**
     * 现居住地省
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    residenceProvince?: number;

    /**
     * 现居住地市
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    residenceCity?: number;

    /**
     * 现居住地县
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    residenceCounty?: number;

    /**
     * 详细现居住地
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    residenceAddress?: string | null;

    /**
     * 工作地址省
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    workProvince?: number | null;

    /**
     * 工作地址市
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    workCity?: number | null;

    /**
     * 工作地址县
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    workCounty?: number | null;

    /**
     * 详细工作地址
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    workAddress?: string | null;

    /**
     * 工作单位
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    workPlace?: string | null;

    /**
     * 单位电话
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    workPlacePhone?: string | null;

    /**
     * 医保类别
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    medInsCategory?: number | null;

    /**
     * @type {MedInsTypeEnum}
     * @memberof CardInfoOutput
     */
    medInsType?: MedInsTypeEnum;

    /**
     * 医疗类别（费别）
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    medCategory?: number | null;

    /**
     * 险种类型
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    insuranceType?: string | null;

    /**
     * @type {YesNoEnum}
     * @memberof CardInfoOutput
     */
    isNoCard?: YesNoEnum;

    /**
     * 医保卡信息
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    medInsCardInfo?: string | null;

    /**
     * 就诊卡号
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    cardNo?: string | null;

    /**
     * 患者唯一号
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    patientNo?: string | null;

    /**
     * 患者Id
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    patientId?: number;

    /**
     * @type {BusinessTypeEnum}
     * @memberof CardInfoOutput
     */
    businessType?: BusinessTypeEnum;

    /**
     * 使用科室
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    useDepts?: string | null;

    /**
     * 充值方式
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    chargeModes?: string | null;

    /**
     * 余额
     *
     * @type {number}
     * @memberof CardInfoOutput
     */
    balance?: number | null;

    /**
     * @type {CardStatusEnum}
     * @memberof CardInfoOutput
     */
    status?: CardStatusEnum;

    /**
     * 患者姓名
     *
     * @type {string}
     * @memberof CardInfoOutput
     */
    patientName?: string | null;
}

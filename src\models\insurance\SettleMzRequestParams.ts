/// 医保登记参数
export interface InsuranceRegister {
	idCardNo: string;
	outpatientNo: string; // 门诊号
	insurance: {
		medicalPoolingCategory: string;
		medicalInsuranceFlag: string;
		medicalInstitutionCode: string;
		medicalInsuranceCardNumber: string;
	};
	name: string;
	acquisitionMethod?: string; // 认证方式
	insurancePatientInfo?: any; // 医保患者信息
	// 诊断
	diagnosticCode?: string; // 诊断代码
	diagnosticName?: string; // 诊断名称
	chargeId?: number;
	registerId: number;
	chargeList: any[]; // 收费
	diagList: any[]; // 诊断列表
	deptId: number;
}
/**
 * 门诊预结算接口请求参数
 */ 
export interface SettleMzPreRequest  {
    /**
     * 医疗统筹类别
     * 4：门诊大病，5：意外伤害，6：普通门诊统筹，其他值见字典 YLTCLB
     */
    p_yltclb: string;
    
    /**
     * 社会保障号码
     */
    p_grbh: string;
    
    /**
     * 姓名（参保病人的姓名）
     */
    p_xm: string;
    
    /**
     * 性别（1:男 2:女 9:不确定）
     */
    p_xb: string;
    
    /**
     * 病历号
     */
    p_blh: string;
    
    /**
     * 费用日期（yyyyMMdd）
     */
    p_fyrq?: Date;
    
    /**
     * 主治医师编码（HIS须保证医师有资格，编码与地纬保持一致）
     */
    p_ysbm: string;
    
    /**
     * 疾病编码
     * • yltclb=‘4’时必须传
     * • yltclb=‘5’或’6’且xzbz=’C’可传空串
     * • xzbz=’D’或’E’时必须传
     * 济南免费用药必须传，格式：jbbm:jbmc
     */
    p_jbbm: string;
    
    /**
     * 卡号
     * 无卡结算：不传
     * 有卡结算：地纬DLL控卡时传""，HIS控卡时传实际卡号
     */
    p_kh?: string;
    
    /**
     * 险种标志（医疗 C，工伤 D，生育 E）
     */
    p_xzbz: string;
    
    /**
     * 普通门诊刷卡标志
     * 门诊统筹：0，纯个账消费：1，其余情况不传
     */
    p_ptmzskbz?: string;
    
    /**
     * 疾病说明（新增补充项目，用于保存疾病的相关说明）
     */
    p_jbsm?: string;
    
    /**
     * 外地就医类别（01本地定点就医，10异地治疗，其他见字典 JYLB）
     */
    p_jylb?: string;
    
    /**
     * 就医医院编码（异地治疗时必传）
     */
    p_jyyybm?: string;
    
    /**
     * 就诊卡号（枣庄增加参数）
     */
    p_jzkh?: string;
    
    /**
     * 门诊类型（济南地区使用：2普通门诊，3急诊，4急诊转住院）
     */
    p_mzlx?: string;
    
    /**
     * 识别码（潍坊持卡就医、跨省异地必传）
     */
    p_sbm?: string;
    
    /**
     * 复位码（潍坊持卡就医必传）
     */
    p_fwm?: string;
    
    /**
     * 免费用药标志（济南社区/村卫生室免费用药时传1，默认0）
     */
    p_mfyybz?: string;
    
    /**
     * 补充项目信息（通过 get_bcxm 获取需录入项）
     */
    p_bcxm_ds?: SettleMzPreBcxm[];
    
    /**
     * psam卡号（忻州省内异地必传）
     */
    p_pasmkh?: string;
    
    /**
     * 二维码（电子社保卡/医保电子凭证二维码或 ectoken）
     */
    p_ewm?: string;
    
    /**
     * 预产期/计生手术时间（威海、淄博 xzbz='E' 时必传）
     */
    p_ycq?: Date | null;
    
    /**
     * 怀孕时间（威海、淄博 xzbz='E' 时必传）
     */
    p_hysj?: Date | null;
    
    /**
     * 结婚证号（淄博 xzbz='E' 计生手术必传）
     */
    p_jhzh?: string;
    
    /**
     * 工伤发生日期（济宁工伤住院登记时使用，非必传）
     */
    p_gsfsrq?: Date | null;
    
    /**
     * 生育合并症（济南专用，非必传）
     */
    p_syhbz?: string;
    
    /**
     * 医疗政策标识（可通过 query_ylzcbs 查询获取）
     */
    p_ylzcbs?: string;
    
    /**
     * 主要诊断（跨省异地门诊大病结算必传）
     */
    p_zyzd?: string;
    
    /**
     * 收费认证方式
     * 00 手工录入身份证号
     * 01 医保电子凭证
     * 02 读居民身份证
     * 03 社会保障卡
     * 04 终端扫码
     * 05 终端扫脸
     * 06 电子社会保障卡
     */
    p_sfrzfs: string;
    
    /**
     * 门诊急诊转诊标志（1急诊，2转诊，3转诊合并急诊）
     */
    p_mzjzzzbz?: string;
    
    /**
     * 外伤标志（1:是，0:否；异地门诊外伤就医时必传）
     */
    p_wsbz?: string;
    
    /**
     * 涉及第三方标志（1:是，0:否；异地门诊外伤标志为1时必传）
     */
    p_sjdsfbz?: string;
    
    /**
     * 医疗统筹类别明细（1102:新冠门诊，其他传空）
     */
    p_yltclbmx?: string;
    
    /**
     * 门诊结算方式（德州使用）
     */
    p_mzjsfs?: string;
    
    /**
     * 意外伤害医保负担比例（德州使用，p_mzjsfs="ywsh" 时必传）
     */
    p_ywshybfdbl?: string;
    
    /**
     * 消费账户标志（1:是，0:否；默认"1"，异地就医使用）
     */
    p_xfzhbz?: string;
    
    /**
     * 就诊科室编码（2024-07-22 起门诊结算必传）
     */
    p_ksbm: string;
    
    /**
     * 多诊断信息（多个诊断时传入）
     */
    p_jbbm_ds?: SettleMzPreJbbm[];
    
    /**
     * 刷脸授权码（医保三类终端刷脸返回的 authNo）
     */
    p_authno?: string;
    
    /**
     * 费用凭单信息
     */
    p_fypd_ds?: SettleMzPreFypd[];
}

/**
 * 补充项目信息
 */
export interface SettleMzPreBcxm {
    /**
     * 补充项目编号
     */
    bcxmbh?: string;
    
    /**
     * 补充项目值
     */
    bcxmz?: string;
}

/**
 * 多诊断信息
 */
export interface SettleMzPreJbbm {
    /**
     * 诊断疾病编码
     */
    dzdjbbm?: string;
    /**
     *   *主诊断标志	0：否，1：是
     */
            maindiag_flag?: string;
     /**
     *诊断类别	1：西医诊断，2：中医主病诊断，3：中医主证诊断，可调用数据字典接口获取，代码编号：DIAG_TYPE
      */
            diag_type?: string;
    /**
    *诊断顺序号
    */
         diag_srt_no ?: number;
   /**
    /// diag_dept
   */
            diag_dept?: string;
   /**
    /// *诊断医师编码	需传入国标医师编码
  */
        diag_dr_code ?: string;
   /**
    /// 诊断医师姓名
     */
            diag_dr_name?: string;
   /**
     *诊断时间
     */
        diag_time ?: Date;
}

/**
 * 费用凭单信息
 */
export interface SettleMzPreFypd {
    /**
     * 医院项目编码
     */
    yyxmbm: string;
    
    /**
     * 单价
     */
    dj: number;
    
    /**
     * 总金额（zje=dj*sl）
     */
    zje: number;
    
    /**
     * 数量
     */
    sl: number;
    
    /**
     * 执行科室编码
     */
    zxksbm: string;
    
    /**
     * 开单科室编码
     */
    kdksbm: string;
    
    /**
     * 自付比例
     */
    sxzfbl: number;
    
    /**
     * 费用发生时间
     */
    fyfssj: string;
    
    /**
     * 医院项目名称
     */
    yyxmmc?: string;
    
    /**
     * 规格
     */
    gg?: string;
    
    /**
     * 大包装的小包装数量
     */
    bzsl?: number | null;
    
    /**
     * 用药天数
     */
    yyts?: number | null;
    
    /**
     * 用药说明
     */
    yysm?: string;
    
    /**
     * 医嘱流水号
     */
    yzlsh?: string;
    
    /**
     * 收费人员姓名
     */
    sfryxm?: string;
    
    /**
     * 给药途径
     */
    gytj?: string;
    
    /**
     * 单次用量
     */
    dcyl?: number | null;
    
    /**
     * 用药频次
     */
    yypc?: string;
    
    /**
     * 外配标志（德州：1是0否）
     */
    wpbz?: string;
    
    /**
     * 执行医师编码
     */
    zxysbm?: string;
    
    /**
     * 开单医师编码
     */
    kdysbm?: string;
    
    /**
     * 追溯码（多个用,分隔）
     */
    zsm?: string;
    
    /**
     * 拆零标志（0否1是）
     */
    clbz?: string;
}

 
/**
 * 门诊结算接口请求参数 
 */
export interface SettleMzRequest extends SettleMzPreRequest {
    /**
     * 个人账户支付金额（大于0时忽略是否使用个账参数）
     */
    grzhzf?: number;

    /**
     * 是否使用个人账户（虚账户地区专用）
     */
    sfsygrzh?: string;

    /**
     * 是否需要返回结算单（1是 0否，默认1）
     */
    needjsd?: string;

    /**
     * 病人ID（诊间审核用）
     */
    hzid?: string;

    /**
     * 就诊标识（诊间审核用）
     */
    jzbs?: string;

    /**
     * 是否本院职工（诊间审核用）
     */
    sfbyzg?: string;

    /**
     * 病区标识（诊间审核用）
     */
    bqbs?: string;

    /**
     * 主诊断编码（诊间审核用）
     */
    zzdbm?: string;

    /**
     * 主诊断名称（诊间审核用）
     */
    zzdmc?: string;

    /**
     * 单病种结算标识（诊间审核用）
     */
    dbzjsbs?: string;

    /**
     * 病房号（诊间审核用）
     */
    bf?: string;

    /**
     * 病床号（诊间审核用）
     */
    cw?: string;

    /**
     * 病种类型（诊间审核用）
     */
    bzlx?: string;

    /**
     * 病种代码（诊间审核用）
     */
    bzdm?: string;

    /**
     * 病种名称（诊间审核用）
     */
    bzmc?: string;

    /**
     * 医疗类别（诊间审核用）
     */
    yllb?: string;

    /**
     * 生育状态（诊间审核用）
     */
    syzt?: string;

    /**
     * 多诊断信息（诊间审核用）
     */
    Dzd?: SettleMzDzd[];
}

/**
 * 多诊断信息结构体（诊间审核用）
 */
export interface SettleMzDzd {
    /**
     * 诊断编码（ICD-10）
     */
    zdbm?: string;

    /**
     * 诊断名称
     */
    zdmc?: string;

    /**
     * 诊断类别：1=入院诊断，2=出院诊断
     */
    zdlb?: string;

    /**
     * 诊断序号：0=主要诊断，1=第一辅诊，2=第二辅诊...
     */
    zdlb2?: string;

    /**
     * 诊断内容（完整诊断描述）
     */
    zdnr?: string;

    /**
     * 入院病情：1.危 2.急 3.一般
     */
    rybq?: string;

    /**
     * 诊断时间，格式：yyyyMMddHHmmss
     */
    zdsj?: string;
}
